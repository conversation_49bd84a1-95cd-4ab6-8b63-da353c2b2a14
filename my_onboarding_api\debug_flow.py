#!/usr/bin/env python3
"""
Debug script to test the signup flow step by step.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.session_service import SessionService
from app.models.session import FlowType, FlowStep
from app.core.logging import get_logger

logger = get_logger(__name__)


def debug_flow_steps():
    """Debug the flow step progression."""
    print("Debugging Signup Flow Steps")
    print("=" * 50)
    
    session_service = SessionService()
    session_id = "debug_session"
    
    # Start signup flow
    print("1. Starting signup flow...")
    session_service.start_flow(session_id, FlowType.SIGNUP)
    
    session = session_service.get_session(session_id)
    print(f"Initial flow step: {session.flow_step}")
    print(f"Flow type: {session.flow_type}")
    print(f"Is flow active: {session_service.is_flow_active(session_id)}")
    
    # Test first few steps manually
    test_steps = [
        ("name", "<PERSON>"),
        ("firstN<PERSON>", "<PERSON>"),
        ("lastName", "Do<PERSON>"),
        ("email", "<EMAIL>"),
        ("password", "password123")
    ]
    
    print("\n2. Testing first 5 steps...")
    for i, (expected_field, response) in enumerate(test_steps):
        print(f"\n--- Step {i+1} ---")
        
        session = session_service.get_session(session_id)
        current_step = session.flow_step
        
        print(f"Current step: {current_step}")
        print(f"Expected field: {expected_field}")
        print(f"Is flow active: {session_service.is_flow_active(session_id)}")
        
        if not session_service.is_flow_active(session_id):
            print("❌ Flow is not active - this is the problem!")
            break
            
        if current_step.value != expected_field:
            print(f"❌ Step mismatch! Expected {expected_field}, got {current_step.value}")
        
        try:
            # Get the flow message
            message = session_service.get_flow_message(session_id)
            print(f"Flow message: {message}")
            
            # Advance the flow
            print(f"Advancing with response: {response}")
            next_step = session_service.advance_flow(session_id, current_step.value, response)
            
            print(f"Next step: {next_step}")
            
            # Check collected data
            session = session_service.get_session(session_id)
            print(f"Collected data so far: {session.collected_data}")
            
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            break
    
    print("\n3. Final session state:")
    session = session_service.get_session(session_id)
    print(f"Flow step: {session.flow_step}")
    print(f"Flow type: {session.flow_type}")
    print(f"Is flow active: {session_service.is_flow_active(session_id)}")
    print(f"Collected data: {session.collected_data}")


def test_all_flow_steps():
    """Test all flow steps to see where it breaks."""
    print("\n\nTesting All Flow Steps")
    print("=" * 50)
    
    # Get all signup flow steps
    signup_flow = [
        FlowStep.FIRST_NAME,
        FlowStep.LAST_NAME,
        FlowStep.EMAIL,
        FlowStep.PASSWORD,
        FlowStep.PHONE_NUMBER,
        FlowStep.CITY_ID,
        FlowStep.CITY,
        FlowStep.STATE,
        FlowStep.ZIP_CODE,
        FlowStep.STREET_ADDRESS_ONE,
        FlowStep.STREET_ADDRESS_TWO,
        FlowStep.ROLE,
        FlowStep.ASSIGNED_ROLE,
        FlowStep.USER_TYPE,
        FlowStep.TYPE,
        FlowStep.REGISTERED_FROM,
        FlowStep.USER_ADDED_FROM,
        FlowStep.CREATED_BY,
        FlowStep.IS_STAKEHOLDER,
        FlowStep.IS_ASSOCIATED,
        FlowStep.GENDER,
        FlowStep.BIRTHDAY,
        FlowStep.CITIES_ARRAY
    ]
    
    print(f"Total steps in signup flow: {len(signup_flow)}")
    
    session_service = SessionService()
    session_id = "test_all_steps"
    
    # Start flow
    session_service.start_flow(session_id, FlowType.SIGNUP)
    
    step_count = 0
    while session_service.is_flow_active(session_id) and step_count < 30:  # Safety limit
        step_count += 1
        session = session_service.get_session(session_id)
        current_step = session.flow_step
        
        print(f"Step {step_count}: {current_step.value}")
        
        # Provide a test response
        if current_step.value in ["isStakeholder", "isAssociated"]:
            response = "no"
        elif current_step.value == "citiesArray":
            response = "city1,city2"
        elif current_step.value == "birthday":
            response = "1990-01-01"
        elif current_step.value == "zipCode":
            response = "12345"
        elif current_step.value == "phoneNumber":
            response = "555-1234"
        else:
            response = f"test_{current_step.value}"
        
        try:
            next_step = session_service.advance_flow(session_id, current_step.value, response)
            print(f"  → Next step: {next_step}")
        except Exception as e:
            print(f"  ❌ ERROR: {str(e)}")
            break
    
    print(f"\nCompleted {step_count} steps")
    
    if session_service.is_flow_active(session_id):
        print("❌ Flow is still active - didn't complete all steps")
    else:
        print("✅ Flow completed successfully")
        
        # Try to complete the flow
        try:
            user_data = session_service.complete_flow(session_id)
            data_dict = user_data.to_dict()
            print(f"Final collected data: {len(data_dict)} fields")
            for key, value in data_dict.items():
                print(f"  {key}: {value}")
        except Exception as e:
            print(f"❌ ERROR completing flow: {str(e)}")


if __name__ == "__main__":
    debug_flow_steps()
    test_all_flow_steps()
