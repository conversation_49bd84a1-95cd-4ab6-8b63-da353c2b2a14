"""
Authentication service for external API integration.

This module handles authentication with external services.
"""

from typing import Dict, Any, Optional
import requests
from requests.exceptions import RequestException, Timeout

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import AuthenticationError, ExternalServiceError
from ..models.requests import LoginRequest, SignupRequest

logger = get_logger(__name__)


class AuthenticationService:
    """Service for handling authentication with external APIs."""
    
    def __init__(self):
        """Initialize the authentication service."""
        self.auth_url = settings.auth_api_url
        self.signup_url = settings.signup_api_url
        self.timeout = settings.auth_api_timeout
        self.bearer_token = settings.api_bearer_token
    
    def login(self, login_data: LoginRequest) -> Dict[str, Any]:
        """
        Authenticate user with external API.
        
        Args:
            login_data: Login request data
            
        Returns:
            Authentication response
            
        Raises:
            AuthenticationError: If authentication fails
            ExternalServiceError: If the API call fails
        """
        try:
            logger.info(f"Attempting login for user: {login_data.email}")
            
            # Prepare request data
            request_data = {
                "email": login_data.email,
                "password": login_data.password
            }
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "OnboardingAPI/1.0"
            }
            
            # Make the API call
            response = requests.post(
                self.auth_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )
            
            # Parse response
            try:
                response_data = response.json()
            except ValueError as e:
                logger.error(f"Invalid JSON response from auth API: {str(e)}")
                raise ExternalServiceError(
                    service_name="authentication",
                    message="Invalid response format from authentication service",
                    status_code=response.status_code
                )
            
            # Check for successful authentication
            if response.status_code == 200 and isinstance(response_data, dict):
                if response_data.get('success') is True:
                    logger.info(f"Login successful for user: {login_data.email}")
                    return {
                        "status": "success",
                        "message": "Login successful",
                        "data": response_data
                    }
                
                # Authentication failed - raise with exact response body and status
                error_msg = response_data.get('message', response_data)
                logger.warning(f"Login failed for user {login_data.email}: {error_msg}")

                # Attach the full response_data so callers can inspect or forward it
                raise AuthenticationError(
                    message=str(error_msg),
                    details={
                        "email": login_data.email,
                        "response_data": response_data
                    },
                    status_code=response.status_code
                )
            
            # Unexpected response - include response body and status to help callers
            logger.error(f"Unexpected response from auth API: {response.status_code}")
            raise ExternalServiceError(
                service_name="authentication",
                message="Unexpected response from authentication service",
                status_code=response.status_code,
                details={"response_data": response_data}
            )
            
        except Timeout:
            logger.error(f"Authentication API timeout for user: {login_data.email}")
            raise ExternalServiceError(
                service_name="authentication",
                message="Authentication service timeout",
                details={"timeout": self.timeout}
            )
            
        except RequestException as e:
            logger.error(f"Authentication API request failed: {str(e)}")
            raise ExternalServiceError(
                service_name="authentication",
                message=f"Authentication service error: {str(e)}"
            )
    
    def signup(self, signup_data: SignupRequest) -> Dict[str, Any]:
        """
        Register a new user with external API.

        Args:
            signup_data: Signup request data

        Returns:
            Signup response

        Raises:
            AuthenticationError: If signup fails
            ExternalServiceError: If the API call fails
        """
        try:
            logger.info(f"Attempting signup for user: {signup_data.email}")

            # Prepare request data with all the parameters
            request_data = {
                "cityId": signup_data.cityId,
                "firstName": signup_data.firstName,
                "lastName": signup_data.lastName,
                "email": signup_data.email,
                "phoneNumber": signup_data.phoneNumber,
                "name": signup_data.name,
                "role": signup_data.role,
                "assignedRole": signup_data.assignedRole,
                "registeredFrom": signup_data.registeredFrom,
                "isStakeholder": signup_data.isStakeholder,
                "userAddedFrom": signup_data.userAddedFrom,
                "citiesArray": signup_data.citiesArray,
                "isAssociated": signup_data.isAssociated,
                "gender": signup_data.gender,
                "streetAddressOne": signup_data.streetAddressOne,
                "streetAddressTwo": signup_data.streetAddressTwo,
                "birthday": signup_data.birthday,
                "city": signup_data.city,
                "state": signup_data.state,
                "zipCode": signup_data.zipCode,
                "type": signup_data.type,
                "createdBy": signup_data.createdBy,
                "userType": signup_data.userType,
                "password": signup_data.password
            }

            # Set default values for required fields if they're None
            if request_data.get('role') is None:
                request_data['role'] = 'MEMBER'
            if request_data.get('assignedRole') is None:
                request_data['assignedRole'] = 'MEMBER'
            if request_data.get('userType') is None:
                request_data['userType'] = 'loginUser'
                
            # Remove None values for other fields, but keep the ones we just set
            request_data = {k: v for k, v in request_data.items() if v is not None or k in ['role', 'assignedRole', 'userType']}

            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "OnboardingAPI/1.0"
            }

            # Add Authorization header if bearer token is available
            if self.bearer_token:
                headers["Authorization"] = f"Bearer {self.bearer_token}"

            # Log the request payload for debugging
            logger.info(
                "Sending signup request to API. "
                f"URL: {self.signup_url}\n"
                "Headers: " + ", ".join(f"{k}: {v}" for k, v in headers.items()) + "\n"
                f"Payload: {request_data}"
            )

            # Make the API call
            response = requests.post(
                self.signup_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )

            # Parse response
            try:
                response_data = response.json()
                logger.debug(f"Signup API response: {response.status_code} - {response_data}")
            except ValueError:
                error_msg = f"Invalid JSON response from signup API: {response.text}"
                logger.error(error_msg)
                response_data = {"error": "Invalid response format from server"}
                raise ExternalServiceError(
                    service_name="signup",
                    message=error_msg,
                    status_code=response.status_code,
                    details={"response_text": response.text}
                )

            # Check for successful signup
            if response.status_code == 200 and isinstance(response_data, dict):
                if response_data.get('success') is True or response.status_code == 200:
                    logger.info(f"Signup successful for user: {signup_data.email}")

                    # Extract user data from response (don't include password)
                    user_data = {
                        "name": signup_data.name,
                        "firstName": signup_data.firstName,
                        "lastName": signup_data.lastName,
                        "email": signup_data.email,
                        "phoneNumber": signup_data.phoneNumber,
                        "cityId": signup_data.cityId,
                        "city": signup_data.city,
                        "role": signup_data.role,
                    }

                    # Remove None values from user data
                    user_data = {k: v for k, v in user_data.items() if v is not None}

                    return {
                        "status": "success",
                        "message": "Signup completed successfully",
                        "user": user_data,
                        "data": response_data
                    }

                # Signup failed - extract error message from different possible response formats
                error_msg = (
                    response_data.get('message') or 
                    response_data.get('error') or 
                    response_data.get('detail') or 
                    'Signup failed. Please check your information and try again.'
                )
                
                # Log detailed error information
                logger.warning(
                    f"Signup failed for user {signup_data.email}. "
                    f"Status: {response.status_code}, "
                    f"Response: {response_data}"
                )

                raise AuthenticationError(
                    message=error_msg,
                    details={
                        "email": signup_data.email,
                        "status_code": response.status_code,
                        "response_data": response_data
                    }
                )

            # Handle non-200 responses
            error_msg = response_data.get('message') or response_data.get('error') or f"Unexpected status code: {response.status_code}"
            logger.error(
                f"Unexpected response from signup API. "
                f"Status: {response.status_code}, "
                f"Response: {response_data}"
            )
            
            raise ExternalServiceError(
                service_name="signup",
                message=error_msg,
                status_code=response.status_code,
                details={"response_data": response_data}
            )

        except Timeout:
            logger.error(f"Signup API timeout for user: {signup_data.email}")
            raise ExternalServiceError(
                service_name="signup",
                message="Signup service timeout",
                details={"timeout": self.timeout}
            )

        except RequestException as e:
            logger.error(f"Signup API request failed: {str(e)}")
            raise ExternalServiceError(
                service_name="signup",
                message=f"Signup service error: {str(e)}"
            )
    
    def validate_credentials(self, email: str, password: str) -> bool:
        """
        Validate user credentials format.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            True if credentials format is valid
        """
        if not email or "@" not in email:
            return False
        
        if not password or len(password.strip()) == 0:
            return False
        
        return True


# Global service instance
auth_service = AuthenticationService()
