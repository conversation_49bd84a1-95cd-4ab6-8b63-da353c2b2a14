"""
Custom exception classes for the application.

This module defines application-specific exceptions for better error handling.
"""

from typing import Optional, Dict, Any
from fastapi import HTT<PERSON>Exception, status


class OnboardingAPIException(Exception):
    """Base exception for the onboarding API."""
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ConfigurationError(OnboardingAPIException):
    """Raised when there's a configuration error."""
    pass


class ExternalServiceError(OnboardingAPIException):
    """Raised when an external service call fails."""
    
    def __init__(
        self,
        service_name: str,
        message: str,
        status_code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.service_name = service_name
        self.status_code = status_code
        super().__init__(message, details)


class IntentClassificationError(OnboardingAPIException):
    """Raised when intent classification fails."""
    pass


class SessionError(OnboardingAPIException):
    """Raised when there's a session-related error."""
    pass


class ValidationError(OnboardingAPIException):
    """Raised when input validation fails."""
    pass


class AuthenticationError(ExternalServiceError):
    """Raised when authentication fails."""
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None,
        status_code: Optional[int] = 401
    ):
        """Allow specifying an HTTP status code returned by the auth service.

        This preserves backward compatibility while enabling callers to forward
        the exact status code returned by the upstream authentication API.
        """
        super().__init__(
            service_name="authentication",
            message=message,
            status_code=status_code,
            details=details
        )


class RateLimitError(OnboardingAPIException):
    """Raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None
    ):
        details = {"retry_after": retry_after} if retry_after else {}
        super().__init__(message, details)


# HTTP Exception helpers
def create_http_exception(
    status_code: int,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """
    Create an HTTPException with structured error response.
    
    Args:
        status_code: HTTP status code
        message: Error message
        details: Additional error details
        
    Returns:
        HTTPException instance
    """
    error_detail = {
        "error": message,
        "status_code": status_code
    }
    
    if details:
        error_detail["details"] = details
    
    return HTTPException(
        status_code=status_code,
        detail=error_detail
    )


def validation_error(message: str, field: Optional[str] = None) -> HTTPException:
    """Create a validation error HTTP exception."""
    details = {"field": field} if field else {}
    return create_http_exception(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message=message,
        details=details
    )


def internal_server_error(message: str = "Internal server error") -> HTTPException:
    """Create an internal server error HTTP exception."""
    return create_http_exception(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message=message
    )


def service_unavailable_error(service_name: str) -> HTTPException:
    """Create a service unavailable error HTTP exception."""
    return create_http_exception(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        message=f"{service_name} service is currently unavailable",
        details={"service": service_name}
    )


def rate_limit_error(retry_after: Optional[int] = None) -> HTTPException:
    """Create a rate limit error HTTP exception."""
    details = {"retry_after": retry_after} if retry_after else {}
    return create_http_exception(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        message="Rate limit exceeded",
        details=details
    )
