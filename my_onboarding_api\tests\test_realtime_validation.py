#!/usr/bin/env python3
"""
Test script to verify real-time field validation during the flow.
"""

import requests
import json
import sys
import uuid

def test_realtime_email_validation():
    """Test that email validation happens during the flow, not at the end."""
    base_url = "http://localhost:8000"
    session_id = f"test-session-{uuid.uuid4()}"
    
    print("⚡ Testing Real-time Email Validation")
    print("=" * 50)
    print(f"Session ID: {session_id}")
    
    try:
        # Step 1: Initiate login flow
        print("\n📝 Step 1: Initiating login flow...")
        
        payload = {"message": "I want to log in"}
        headers = {
            "Content-Type": "application/json",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to initiate login flow: {response.status_code}")
            return False
        
        data = response.json()
        print(f"✅ Login flow initiated")
        print(f"Expected: flow_step='email', flow_type='login'")
        print(f"Actual: flow_step='{data.get('flow_step')}', flow_type='{data.get('flow_type')}'")
        
        if data.get("flow_step") != "email" or data.get("flow_type") != "login":
            print("❌ Unexpected flow state")
            return False
        
        # Step 2: Provide INVALID email - should get validation error immediately
        print("\n📧 Step 2: Providing INVALID email ('vimal')...")
        print("Expected: Should get validation error and stay on email step")
        
        payload = {"message": "vimal"}  # Invalid email format
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Got HTTP error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        # Check if we got a validation error and stayed on email step
        if (data.get("flow_step") == "email" and 
            "email" in data.get("message", "").lower() and 
            ("invalid" in data.get("message", "").lower() or "valid" in data.get("message", "").lower())):
            print("✅ Got real-time email validation error and stayed on email step!")
        else:
            print("❌ Did not get expected validation behavior")
            print(f"Expected: flow_step='email' with validation message")
            print(f"Actual: flow_step='{data.get('flow_step')}', message='{data.get('message')}'")
            return False
        
        # Step 3: Provide VALID email - should advance to password step
        print("\n📧 Step 3: Providing VALID email ('<EMAIL>')...")
        print("Expected: Should advance to password step")
        
        payload = {"message": "<EMAIL>"}  # Valid email format
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed with valid email: {response.status_code}")
            return False
        
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        # Check if we advanced to password step
        if data.get("flow_step") == "password":
            print("✅ Successfully advanced to password step with valid email!")
        else:
            print("❌ Did not advance to password step")
            print(f"Expected: flow_step='password'")
            print(f"Actual: flow_step='{data.get('flow_step')}'")
            return False
        
        # Step 4: Provide password - should complete the flow
        print("\n🔑 Step 4: Providing password...")
        print("Expected: Should complete login flow (success or auth failure)")
        
        payload = {"message": "testpassword123"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Flow completed successfully")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            # Should not have flow_step anymore (flow completed)
            if "flow_step" not in data:
                print("✅ Flow properly completed (no flow_step in response)")
            else:
                print(f"⚠️  Flow still active: flow_step='{data.get('flow_step')}'")
            
            return True
        else:
            print(f"❌ Failed to complete flow: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signup_validation():
    """Test validation in signup flow."""
    base_url = "http://localhost:8000"
    session_id = f"test-session-{uuid.uuid4()}"
    
    print("\n👤 Testing Signup Flow Validation")
    print("=" * 40)
    print(f"Session ID: {session_id}")
    
    try:
        # Initiate signup
        payload = {"message": "I want to sign up"}
        headers = {
            "Content-Type": "application/json",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to initiate signup: {response.status_code}")
            return False
        
        # Provide name
        payload = {"message": "John Doe"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to provide name: {response.status_code}")
            return False
        
        # Provide invalid email
        print("📧 Testing invalid email in signup...")
        payload = {"message": "invalid-email"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed email step: {response.status_code}")
            return False
        
        data = response.json()
        
        # Should stay on email step with validation error
        if (data.get("flow_step") == "email" and 
            "email" in data.get("message", "").lower()):
            print("✅ Signup email validation working!")
            return True
        else:
            print("❌ Signup email validation not working")
            return False
        
    except Exception as e:
        print(f"❌ Signup test failed: {e}")
        return False

def main():
    """Run the real-time validation tests."""
    print("🧪 Real-time Field Validation Test Suite")
    print("=" * 70)
    
    # Check server
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server not healthy")
            return False
    except:
        print("❌ Server not accessible")
        return False
    
    print("✅ Server is running")
    
    # Run tests
    login_result = test_realtime_email_validation()
    signup_result = test_signup_validation()
    
    print("\n" + "="*70)
    print("📊 Test Results:")
    print(f"   Login Real-time Validation: {'✅ PASS' if login_result else '❌ FAIL'}")
    print(f"   Signup Real-time Validation: {'✅ PASS' if signup_result else '❌ FAIL'}")
    
    if login_result and signup_result:
        print("\n🎉 All real-time validation tests passed!")
        print("✅ Email validation now happens during the flow, not at the end!")
        return True
    else:
        print("\n❌ Some validation tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
