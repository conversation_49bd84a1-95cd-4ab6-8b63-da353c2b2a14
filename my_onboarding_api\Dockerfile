# Use a slim official Python image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8080
ENV RUN_MODE=both  # can be "api", "mcp", or "both"

# Set working directory
WORKDIR /app

# Install dependencies (faster layer caching)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy rest of your code
COPY . .

# Expose the API port
EXPOSE 8080

# Health check for container environments (optional but helpful)
HEALTHCHECK CMD curl --fail http://localhost:8080/ || exit 1

# Run your unified deploy script
CMD ["python", "deploy.py"]
