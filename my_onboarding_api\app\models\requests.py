"""
Request models for API endpoints.

This module defines Pydantic models for request validation.
"""

from typing import Optional, List
from pydantic import BaseModel, Field, field_validator
from ..utils.security import sanitize_input, validate_email, detect_potential_injection


class MessageRequest(BaseModel):
    """Request model for message-based endpoints."""
    
    message: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="User message content",
        example="I want to sign up for an account"
    )
    
    @field_validator("message")
    @classmethod
    def validate_message(cls, v: str) -> str:
        """Validate message content."""
        if not v or not v.strip():
            raise ValueError("Message cannot be empty")

        # Check for potential injection attempts
        if detect_potential_injection(v):
            raise ValueError("Message contains potentially unsafe content")

        # Sanitize and return
        return sanitize_input(v.strip(), max_length=1000)


class LoginRequest(BaseModel):
    """Request model for login endpoint."""
    
    email: str = Field(
        ...,
        description="User email address",
        example="<EMAIL>"
    )
    password: str = Field(
        ...,
        min_length=1,
        description="User password"
    )
    
    @field_validator("email")
    @classmethod
    def validate_email_format(cls, v: str) -> str:
        """Validate email format."""
        if not v or not validate_email(v):
            raise ValueError("Invalid email format")
        return v.lower().strip()

    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password."""
        if not v or not v.strip():
            raise ValueError("Password cannot be empty")
        if len(v) < 1:  # Basic length check
            raise ValueError("Password too short")
        return v


class SignupRequest(BaseModel):
    """Request model for signup endpoint."""

    # Basic user information
    # Make name and name parts optional here: the route enforces that either
    # full name OR firstName+lastName is provided before constructing this
    # model. Keeping them Optional avoids pydantic raising a ValidationError
    # prematurely when the route derives/normalizes name parts.
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="User full name",
        example="John Doe"
    )
    firstName: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=50,
        description="User first name",
        example="John"
    )
    lastName: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=50,
        description="User last name",
        example="Doe"
    )
    email: str = Field(
        ...,
        description="User email address",
        example="<EMAIL>"
    )
    password: str = Field(
        ...,
        min_length=1,
        description="User password"
    )
    phoneNumber: Optional[str] = Field(
        default=None,
        description="User phone number",
        example="+1234567890"
    )

    # Location and city information
    cityId: Optional[str] = Field(
        default=None,
        description="City identifier",
        example="city_123"
    )
    city: Optional[str] = Field(
        default=None,
        max_length=100,
        description="City name",
        example="New York"
    )
    state: Optional[str] = Field(
        default=None,
        max_length=50,
        description="State or province",
        example="NY"
    )
    zipCode: Optional[str] = Field(
        default=None,
        max_length=20,
        description="ZIP or postal code",
        example="10001"
    )
    streetAddressOne: Optional[str] = Field(
        default=None,
        max_length=200,
        description="Primary street address",
        example="123 Main St"
    )
    streetAddressTwo: Optional[str] = Field(
        default=None,
        max_length=200,
        description="Secondary street address",
        example="Apt 4B"
    )

    # Role and permissions
    role: Optional[str] = Field(
        default=None,
        description="User role",
        example="citizen"
    )
    assignedRole: Optional[str] = Field(
        default=None,
        description="Assigned role",
        example="resident"
    )
    userType: Optional[str] = Field(
        default=None,
        description="Type of user",
        example="individual"
    )
    type: Optional[str] = Field(
        default=None,
        description="Account type",
        example="standard"
    )

    # Registration metadata
    registeredFrom: Optional[str] = Field(
        default=None,
        description="Registration source",
        example="web_app"
    )
    userAddedFrom: Optional[str] = Field(
        default=None,
        description="Source where user was added from",
        example="self_registration"
    )
    createdBy: Optional[str] = Field(
        default=None,
        description="User ID who created this account",
        example="admin_123"
    )

    # Flags and associations
    isStakeholder: Optional[bool] = Field(
        default=False,
        description="Whether user is a stakeholder"
    )
    isAssociated: Optional[bool] = Field(
        default=False,
        description="Whether user is associated with an organization"
    )

    # Additional information
    gender: Optional[str] = Field(
        default=None,
        description="User gender",
        example="prefer_not_to_say"
    )
    birthday: Optional[str] = Field(
        default=None,
        description="User birthday in YYYY-MM-DD format",
        example="1990-01-01"
    )

    # Cities array for multi-city access
    citiesArray: Optional[List[str]] = Field(
        default=None,
        description="Array of city IDs user has access to",
        example=["city_123", "city_456"]
    )
    
    @field_validator("name", "firstName", "lastName")
    @classmethod
    def validate_name_fields(cls, v: Optional[str]) -> Optional[str]:
        """Validate name fields. Allow None since route ensures at least one
        form of name is present before creating the SignupRequest."""
        if v is None:
            return v

        if not v or not v.strip():
            raise ValueError("Name field cannot be empty")

        # Sanitize name input
        sanitized = sanitize_input(v.strip(), max_length=100)
        if not sanitized:
            raise ValueError("Name contains invalid characters")

        return sanitized

    @field_validator("email")
    @classmethod
    def validate_email_format(cls, v: str) -> str:
        """Validate email format."""
        if not v or not validate_email(v):
            raise ValueError("Invalid email format")
        return v.lower().strip()

    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password."""
        if not v or not v.strip():
            raise ValueError("Password cannot be empty")
        if len(v) < 1:  # Basic length check
            raise ValueError("Password too short")
        return v

    @field_validator("phoneNumber")
    @classmethod
    def validate_phone_number(cls, v: Optional[str]) -> Optional[str]:
        """Validate phone number format."""
        if v is None:
            return v

        # Basic phone number validation - remove spaces and check format
        cleaned = v.strip().replace(" ", "").replace("-", "").replace("(", "").replace(")", "")
        if cleaned and not cleaned.replace("+", "").isdigit():
            raise ValueError("Invalid phone number format")

        return v.strip() if v else None

    @field_validator("zipCode")
    @classmethod
    def validate_zip_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate ZIP code format."""
        if v is None:
            return v

        # Basic ZIP code validation
        cleaned = v.strip()
        if cleaned and not (cleaned.isdigit() or (len(cleaned) == 10 and cleaned[5] == "-")):
            # Allow formats like 12345 or 12345-6789
            if not (len(cleaned) == 5 and cleaned.isdigit()):
                raise ValueError("Invalid ZIP code format")

        return cleaned if cleaned else None

    @field_validator("birthday")
    @classmethod
    def validate_birthday(cls, v: Optional[str]) -> Optional[str]:
        """Validate birthday format (YYYY-MM-DD)."""
        if v is None:
            return v

        # Basic date format validation
        import re
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', v.strip()):
            raise ValueError("Birthday must be in YYYY-MM-DD format")

        return v.strip()

    @field_validator("citiesArray")
    @classmethod
    def validate_cities_array(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate cities array."""
        if v is None:
            return v

        # Ensure all city IDs are strings and not empty
        validated_cities = []
        for city_id in v:
            if not isinstance(city_id, str) or not city_id.strip():
                raise ValueError("All city IDs must be non-empty strings")
            validated_cities.append(city_id.strip())

        return validated_cities if validated_cities else None
