#!/usr/bin/env python3
"""
Clean restart script - clears cache and restarts the service.
"""

import os
import sys
import shutil
from pathlib import Path

def clean_cache():
    """Clean Python cache files."""
    print("🧹 Cleaning Python cache...")
    
    # Remove __pycache__ directories
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                cache_path = os.path.join(root, dir_name)
                print(f"   Removing: {cache_path}")
                shutil.rmtree(cache_path, ignore_errors=True)
    
    # Remove .pyc files
    for root, dirs, files in os.walk("."):
        for file_name in files:
            if file_name.endswith(".pyc"):
                pyc_path = os.path.join(root, file_name)
                print(f"   Removing: {pyc_path}")
                os.remove(pyc_path)
    
    print("✅ Cache cleaned!")

def test_import():
    """Test importing the service."""
    try:
        print("🧪 Testing service import...")
        from app.services.intent_service import IntentClassificationService
        
        service = IntentClassificationService()
        print(f"✅ Service created, pipeline ready: {service.is_pipeline_ready()}")
        
        # Quick test
        if service.is_pipeline_ready():
            result = service.classify_intent("hello")
            print(f"✅ Quick test passed: {len(result)} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 Clean Restart Process")
    print("=" * 30)
    
    # Clean cache
    clean_cache()
    
    # Test import
    if test_import():
        print("\n🎉 Ready to restart the API!")
        print("Run: python run.py")
    else:
        print("\n❌ Issues found, check the errors above")
        sys.exit(1)
