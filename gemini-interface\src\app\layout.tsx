import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "My Village AI - Your Community Assistant",
  description: "Connect with My Village AI, your intelligent community assistant designed to help with local information, support, and guidance.",
  keywords: ["AI assistant", "community", "village", "local support", "chatbot"],
  authors: [{ name: "My Village AI Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#7fb332" },
    { media: "(prefers-color-scheme: dark)", color: "#9cc954" }
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background text-foreground`}
      >
        <div className="relative min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
