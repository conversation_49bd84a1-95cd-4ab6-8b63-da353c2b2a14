#!/usr/bin/env python3
"""
Test script for the intent classification service.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_intent_service():
    """Test the intent classification service."""
    try:
        print("🧪 Testing Intent Classification Service")
        print("=" * 50)
        
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        
        # Check environment variables
        hf_token = os.getenv("HF_TOKEN")
        model_name = os.getenv("MODEL_NAME")
        
        if not hf_token:
            print("❌ HF_TOKEN not found in .env file")
            return False
        
        if not model_name:
            print("❌ MODEL_NAME not found in .env file")
            return False
        
        print(f"✅ HF_TOKEN: {hf_token[:10]}...")
        print(f"✅ MODEL_NAME: {model_name}")
        
        # Import the service
        print("\n📦 Importing intent service...")
        from app.services.intent_service import IntentClassificationService
        
        # Create service instance
        print("🚀 Creating service instance...")
        service = IntentClassificationService()
        
        # Check if pipeline is ready
        if not service.is_pipeline_ready():
            print("❌ Pipeline not ready")
            return False
        
        print("✅ Pipeline ready")
        
        # Test messages
        test_messages = [
            "I want to sign up for an account",
            "I need to log in",
            "Hello, how are you?",
            "What can you help me with?"
        ]
        
        print("\n🧪 Testing intent classification...")
        for i, message in enumerate(test_messages, 1):
            print(f"\n--- Test {i}: '{message}' ---")
            
            try:
                # Test classify_intent method
                results = service.classify_intent(message)
                print(f"✅ Classification successful")
                print(f"📊 Results count: {len(results)}")
                
                for j, result in enumerate(results):
                    print(f"   {j+1}. {result.label}: {result.score:.3f}")
                
                # Test get_top_intent method
                top_intent = service.get_top_intent(message)
                print(f"🎯 Top intent: {top_intent}")
                
            except Exception as e:
                print(f"❌ Classification failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_raw_pipeline():
    """Test the raw Hugging Face pipeline."""
    try:
        print("\n🔍 Testing Raw Pipeline")
        print("=" * 30)
        
        from transformers import pipeline
        from dotenv import load_dotenv
        load_dotenv()
        
        hf_token = os.getenv("HF_TOKEN")
        model_name = os.getenv("MODEL_NAME")
        
        os.environ["HUGGINGFACEHUB_API_TOKEN"] = hf_token
        
        print("🚀 Creating raw pipeline...")
        pipe = pipeline(
            "text-classification",
            model=model_name,
            return_all_scores=True
        )
        
        test_text = "I want to sign up"
        print(f"🧪 Testing with: '{test_text}'")
        
        results = pipe(test_text)
        print(f"📊 Raw results type: {type(results)}")
        print(f"📊 Raw results: {results}")
        
        return True
        
    except Exception as e:
        print(f"❌ Raw pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 Intent Service Test Suite")
    print("=" * 60)
    
    # Test raw pipeline first
    if not test_raw_pipeline():
        print("❌ Raw pipeline test failed")
        sys.exit(1)
    
    # Test intent service
    if not test_intent_service():
        print("❌ Intent service test failed")
        sys.exit(1)
    
    print("\n🎉 All tests completed successfully!")
    sys.exit(0)
