"""
Gemini AI service for chat functionality.

This module provides integration with Google's Gemini AI.
"""

from typing import List, Optional, Dict, Any
import google.generativeai as genai

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import ExternalServiceError, ConfigurationError
from ..models.responses import GeminiModel

logger = get_logger(__name__)


class GeminiService:
    """Service for interacting with Google's Gemini AI."""
    
    def __init__(self):
        """Initialize the Gemini service."""
        self._model = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Gemini client."""
        try:
            logger.info("Initializing Gemini AI client")
            
            # Configure the API key
            genai.configure(api_key=settings.gemini_api_key)
            
            # Initialize the model
            self._model = genai.GenerativeModel(settings.gemini_model)
            
            logger.info(f"Gemini AI client initialized with model: {settings.gemini_model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {str(e)}")
            raise ConfigurationError(
                f"Failed to initialize Gemini AI: {str(e)}",
                details={"model": settings.gemini_model}
            )
    
    def generate_response(self, prompt: str) -> str:
        """
        Generate a response using Gemini AI.
        
        Args:
            prompt: Input prompt for the AI
            
        Returns:
            Generated response text
            
        Raises:
            ExternalServiceError: If the API call fails
        """
        if not self._model:
            raise ExternalServiceError(
                service_name="gemini",
                message="Gemini model not initialized"
            )
        
        if not prompt or not prompt.strip():
            raise ValueError("Prompt cannot be empty")
        
        try:
            logger.debug(f"Generating Gemini response for prompt: {prompt[:100]}...")
            
            # Generate content
            response = self._model.generate_content(prompt.strip())
            
            if not response or not response.text:
                raise ExternalServiceError(
                    service_name="gemini",
                    message="Empty response from Gemini AI"
                )
            
            logger.debug(f"Gemini response generated successfully: {len(response.text)} characters")
            
            return response.text
            
        except Exception as e:
            logger.error(f"Gemini API call failed: {str(e)}")
            raise ExternalServiceError(
                service_name="gemini",
                message=f"Failed to generate response: {str(e)}",
                details={"prompt": prompt[:100]}
            )
    
    def generate_chat_response(
        self,
        user_message: str,
        detected_intent: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate a chat response with context.
        
        Args:
            user_message: User's message
            detected_intent: Detected intent (if any)
            context: Additional context information
            
        Returns:
            Generated chat response
        """
        # Build the prompt with context
        prompt_parts = [f"The user said: '{user_message}'."]
        
        if detected_intent:
            prompt_parts.append(f"The detected intent is: '{detected_intent}'.")
        
        if context:
            for key, value in context.items():
                prompt_parts.append(f"{key}: {value}")
        
        prompt_parts.append("Respond helpfully and naturally, taking into account the intent and context.")
        
        prompt = "\n".join(prompt_parts)
        
        return self.generate_response(prompt)
    
    def list_available_models(self) -> List[GeminiModel]:
        """
        List available Gemini models.
        
        Returns:
            List of available models
            
        Raises:
            ExternalServiceError: If the API call fails
        """
        try:
            logger.debug("Fetching available Gemini models")
            
            models = genai.list_models()
            
            available_models = [
                GeminiModel(
                    name=model.name,
                    supported_generation_methods=model.supported_generation_methods
                )
                for model in models
            ]
            
            logger.debug(f"Found {len(available_models)} available Gemini models")
            
            return available_models
            
        except Exception as e:
            logger.error(f"Failed to list Gemini models: {str(e)}")
            raise ExternalServiceError(
                service_name="gemini",
                message=f"Failed to list models: {str(e)}"
            )
    
    def is_service_ready(self) -> bool:
        """Check if the service is ready for use."""
        return self._model is not None


# Global service instance
gemini_service = GeminiService()
