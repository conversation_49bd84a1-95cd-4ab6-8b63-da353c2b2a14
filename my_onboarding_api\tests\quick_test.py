#!/usr/bin/env python3
"""
Quick test to debug the pipeline issue.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment
load_dotenv()

def test_pipeline():
    """Test the pipeline directly."""
    try:
        from transformers import pipeline
        
        # Set token
        hf_token = os.getenv("HF_TOKEN")
        model_name = os.getenv("MODEL_NAME")
        
        print(f"Token: {hf_token[:10]}...")
        print(f"Model: {model_name}")
        
        os.environ["HUGGINGFACEHUB_API_TOKEN"] = hf_token
        
        # Create pipeline
        print("Creating pipeline...")
        pipe = pipeline(
            "text-classification",
            model=model_name,
            top_k=None,  # Return all scores (replaces deprecated return_all_scores=True)
            device=-1
        )
        
        # Test
        text = "I want to sign up"
        print(f"Testing: {text}")
        
        results = pipe(text)
        print(f"Results type: {type(results)}")
        print(f"Results: {results}")
        
        # Try to process like the service
        if isinstance(results, list):
            if len(results) > 0 and isinstance(results[0], list):
                actual_results = results[0]
            else:
                actual_results = results
            
            print(f"Processed results: {actual_results}")
            
            for result in actual_results:
                print(f"  {result['label']}: {result['score']}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pipeline()
