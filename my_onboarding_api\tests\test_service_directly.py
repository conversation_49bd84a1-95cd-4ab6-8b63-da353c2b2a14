#!/usr/bin/env python3
"""
Test the intent service directly to verify it's working.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_service():
    """Test the intent service directly."""
    try:
        print("🧪 Testing Intent Service Directly")
        print("=" * 40)
        
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        
        # Import and test the service
        from app.services.intent_service import IntentClassificationService
        
        print("📦 Creating service instance...")
        service = IntentClassificationService()
        
        print(f"🔍 Pipeline ready: {service.is_pipeline_ready()}")
        
        if not service.is_pipeline_ready():
            print("❌ Pipeline not ready!")
            return False
        
        # Test messages
        test_messages = [
            "I want to sign up for an account",
            "I need to log in",
            "Hello there"
        ]
        
        for message in test_messages:
            print(f"\n🧪 Testing: '{message}'")
            
            try:
                # Test the classify_intent method
                results = service.classify_intent(message)
                print(f"✅ Classification successful!")
                print(f"📊 Results: {len(results)} intents found")
                
                for i, result in enumerate(results):
                    print(f"   {i+1}. {result.label}: {result.score:.3f}")
                
                # Test get_top_intent
                top_intent = service.get_top_intent(message)
                print(f"🎯 Top intent: {top_intent}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_service()
    sys.exit(0 if success else 1)
