<mxfile host="app.diagrams.net" modified="20231028T083000Z" agent="Mozilla/5.0" version="21.6.8" etag="Yd8QzHjvH8kYQv4W4f7l" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Main Application Flow -->
        <mxCell id="app" value="Onboarding API" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=0;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="120" y="40" width="600" height="700" as="geometry" />
        </mxCell>
        
        <!-- API Endpoints -->
        <mxCell id="endpoints" value="API Endpoints" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="app">
          <mxGeometry y="30" width="600" height="26" as="geometry" />
        </mxCell>
        
        <mxCell id="ep1" value="/onboarding (POST) - Intent classification" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="76" width="560" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ep2" value="/gemini/chat (POST) - Gemini AI chat" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="126" width="560" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ep3" value="/chat (POST) - Combined intent + chat with session" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="176" width="560" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ep4" value="/debug/intent (POST) - Debug intent classification" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="226" width="560" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ep5" value="/gemini/models (GET) - List available Gemini models" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="276" width="560" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ep6" value="/health (GET) - Health check" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;strokeWidth=1;" vertex="1" parent="app">
          <mxGeometry x="20" y="326" width="560" height="40" as="geometry" />
        </mxCell>
        
        <!-- Core Services -->
        <mxCell id="services" value="Core Services" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="app">
          <mxGeometry y="386" width="600" height="26" as="geometry" />
        </mxCell>
        
        <mxCell id="svc1" value="Intent Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="app">
          <mxGeometry x="20" y="432" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="svc2" value="Gemini Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="app">
          <mxGeometry x="220" y="432" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="svc3" value="Auth Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="app">
          <mxGeometry x="420" y="432" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="svc4" value="Session Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="app">
          <mxGeometry x="20" y="512" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- Middleware -->
        <mxCell id="middleware" value="Middleware" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="app">
          <mxGeometry y="592" width="600" height="26" as="geometry" />
        </mxCell>
        
        <mxCell id="mw1" value="CORS Middleware" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="app">
          <mxGeometry x="20" y="638" width="180" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="mw2" value="Security Headers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="app">
          <mxGeometry x="220" y="638" width="160" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="mw3" value="Rate Limiting" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="app">
          <mxGeometry x="400" y="638" width="180" height="40" as="geometry" />
        </mxCell>
        
        <!-- Flows -->
        <mxCell id="flow1" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="ep1" target="svc1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="236" as="sourcePoint" />
            <mxPoint x="230" y="186" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="ep2" target="svc2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="236" as="sourcePoint" />
            <mxPoint x="450" y="186" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow3" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="ep3" target="svc4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="336" as="sourcePoint" />
            <mxPoint x="350" y="286" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
