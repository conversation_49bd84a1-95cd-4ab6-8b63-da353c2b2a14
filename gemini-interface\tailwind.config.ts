import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        // Modern Brand Colors — Sleek & Minimal
        primary: {
          50: "#f2f8ff",
          100: "#dceeff",
          200: "#b9ddff",
          300: "#86c2ff",
          400: "#4da8ff",
          500: "#1e90ff", // Modern blue
          600: "#1675d1",
          700: "#115ba3",
          800: "#0d4175",
          900: "#092b4d",
          950: "#061d33",
        },
        secondary: {
          50: "#fdf7ff",
          100: "#f7ebff",
          200: "#ebd6ff",
          300: "#d6b4ff",
          400: "#b980ff",
          500: "#9b51e0", // Electric violet
          600: "#8437c7",
          700: "#6928a1",
          800: "#4e1d79",
          900: "#351257",
          950: "#220a38",
        },
        accent: {
          50: "#ecfdf5",
          100: "#d1fae5",
          200: "#a7f3d0",
          300: "#6ee7b7",
          400: "#34d399",
          500: "#10b981", // Emerald
          600: "#059669",
          700: "#047857",
          800: "#065f46",
          900: "#064e3b",
          950: "#022c22",
        },
        neutral: {
          50: "#fafafa",
          100: "#f4f4f5",
          200: "#e4e4e7",
          300: "#d4d4d8",
          400: "#a1a1aa",
          500: "#71717a", // Neutral gray
          600: "#52525b",
          700: "#3f3f46",
          800: "#27272a",
          900: "#18181b",
          950: "#0b0b0d",
        },

        // Semantic system colors (using CSS vars for easy dark/light theming)
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: "hsl(var(--card))",
        "card-foreground": "hsl(var(--card-foreground))",
        popover: "hsl(var(--popover))",
        "popover-foreground": "hsl(var(--popover-foreground))",
        "primary-foreground": "hsl(var(--primary-foreground))",
        "secondary-foreground": "hsl(var(--secondary-foreground))",
        "accent-foreground": "hsl(var(--accent-foreground))",
        muted: "hsl(var(--muted))",
        "muted-foreground": "hsl(var(--muted-foreground))",
        destructive: "hsl(var(--destructive))",
        "destructive-foreground": "hsl(var(--destructive-foreground))",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
      },
    }

  },
  plugins: [],
};

export default config;
