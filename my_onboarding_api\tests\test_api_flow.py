#!/usr/bin/env python3
"""
Test script to simulate API calls and test the signup flow.
"""

import sys
import os
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.api.routes import combined_chat_with_intent
from app.models.requests import CombinedChatRequest
from app.services.session_service import session_service


def test_signup_flow_api():
    """Test the signup flow through the API endpoint."""
    print("Testing Signup Flow via API")
    print("=" * 50)
    
    session_id = "test_api_session"
    
    # Step 1: Start signup flow
    print("\n1. Starting signup flow...")
    request = CombinedChatRequest(message="I want to sign up")
    
    try:
        response = combined_chat_with_intent(session_id, request)
        print(f"Response type: {type(response).__name__}")
        print(f"Message: {response.message}")
        print(f"Flow step: {getattr(response, 'flow_step', 'N/A')}")
        print(f"Flow type: {getattr(response, 'flow_type', 'N/A')}")
        
        if not hasattr(response, 'flow_step'):
            print("❌ No flow_step in response - signup flow not started correctly")
            return
            
    except Exception as e:
        print(f"❌ ERROR starting signup: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 2: Test the flow progression
    print("\n2. Testing flow progression...")
    
    test_responses = [
        "John Doe",           # name
        "John",               # firstName  
        "Doe",                # lastName
        "<EMAIL>",   # email
        "password123",        # password
        "******-123-4567",    # phoneNumber
        "city_123",           # cityId
        "New York",           # city
        "NY",                 # state
        "10001",              # zipCode
        "123 Main St",        # streetAddressOne
        "Apt 4B",             # streetAddressTwo
        "citizen",            # role
        "resident",           # assignedRole
        "individual",         # userType
        "standard",           # type
        "web_app",            # registeredFrom
        "self_registration",  # userAddedFrom
        "skip",               # createdBy
        "yes",                # isStakeholder
        "no",                 # isAssociated
        "prefer_not_to_say",  # gender
        "1990-01-15",         # birthday
        "city_123,city_456"   # citiesArray
    ]
    
    step_count = 0
    for user_response in test_responses:
        step_count += 1
        print(f"\n--- Step {step_count} ---")
        
        # Check if flow is still active
        if not session_service.is_flow_active(session_id):
            print(f"Flow completed after {step_count-1} steps")
            break
        
        # Get current session state
        session = session_service.get_session(session_id)
        current_step = session.flow_step.value if session.flow_step else "unknown"
        print(f"Current step: {current_step}")
        print(f"User response: {user_response}")
        
        try:
            # Send the response
            request = CombinedChatRequest(message=user_response)
            response = combined_chat_with_intent(session_id, request)
            
            print(f"Response type: {type(response).__name__}")
            print(f"Message: {response.message}")
            
            if hasattr(response, 'flow_step'):
                print(f"Next flow step: {response.flow_step}")
            elif hasattr(response, 'status'):
                print(f"Status: {response.status}")
                if hasattr(response, 'user') and response.user:
                    print(f"User data: {response.user}")
                break
            
        except Exception as e:
            print(f"❌ ERROR at step {step_count}: {str(e)}")
            import traceback
            traceback.print_exc()
            break
    
    print(f"\nCompleted {step_count} steps")
    
    # Check final session state
    print("\n3. Final session state:")
    if session_service.is_flow_active(session_id):
        session = session_service.get_session(session_id)
        print(f"Flow still active at step: {session.flow_step}")
        print(f"Collected data: {session.collected_data}")
    else:
        print("✅ Flow completed successfully")


def test_skip_functionality():
    """Test skipping optional fields."""
    print("\n\nTesting Skip Functionality")
    print("=" * 50)
    
    session_id = "test_skip_session"
    
    # Start signup
    request = CombinedChatRequest(message="I want to create an account")
    response = combined_chat_with_intent(session_id, request)
    
    if not hasattr(response, 'flow_step'):
        print("❌ Signup flow not started")
        return
    
    # Provide required fields
    required_responses = [
        "Jane Smith",         # name
        "Jane",               # firstName
        "Smith",              # lastName
        "<EMAIL>",   # email
        "password123"         # password
    ]
    
    print("Providing required fields...")
    for i, user_response in enumerate(required_responses):
        if not session_service.is_flow_active(session_id):
            break
            
        request = CombinedChatRequest(message=user_response)
        response = combined_chat_with_intent(session_id, request)
        print(f"Step {i+1}: {user_response} → {getattr(response, 'flow_step', 'completed')}")
    
    # Skip remaining optional fields
    skip_count = 0
    print("\nSkipping optional fields...")
    while session_service.is_flow_active(session_id) and skip_count < 20:
        skip_count += 1
        session = session_service.get_session(session_id)
        current_step = session.flow_step.value if session.flow_step else "unknown"
        
        print(f"Skipping: {current_step}")
        
        request = CombinedChatRequest(message="skip")
        response = combined_chat_with_intent(session_id, request)
        
        if hasattr(response, 'status'):
            print(f"✅ Signup completed with status: {response.status}")
            break
    
    print(f"Skipped {skip_count} optional fields")


if __name__ == "__main__":
    test_signup_flow_api()
    test_skip_functionality()
