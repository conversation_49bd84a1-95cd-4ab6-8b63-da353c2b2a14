#!/usr/bin/env python3
"""
Test script to debug the login flow issue.
"""

import requests
import json
import sys
import uuid

def test_login_flow():
    """Test the complete login flow."""
    base_url = "http://localhost:8000"
    session_id = f"test-session-{uuid.uuid4()}"
    
    print("🔐 Testing Login Flow")
    print("=" * 50)
    print(f"Session ID: {session_id}")
    
    try:
        # Step 1: Initiate login flow
        print("\n📝 Step 1: Initiating login flow...")
        
        payload = {"message": "I want to log in"}
        headers = {
            "Content-Type": "application/json",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        assert response.status_code == 200, f"Failed to initiate login flow: {response.text}"
        
        data = response.json()
        print(f"✅ Login flow initiated")
        print(f"Response: {json.dumps(data, indent=2)}")
        
        assert data.get("flow_type") == "login", f"Expected login flow, got: {data.get('flow_type')}"
        
        # Step 2: Provide email
        print("\n📧 Step 2: Providing email...")
        
        payload = {"message": "<EMAIL>"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        assert response.status_code == 200, f"Failed to provide email: {response.text}"
        if response.status_code != 200:
            print(f"❌ Failed to provide email")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        print(f"✅ Email provided")
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if data.get("flow_step") != "password":
            print(f"❌ Expected password step, got: {data.get('flow_step')}")
            return False
        
        # Step 3: Provide password (this is where the error occurs)
        print("\n🔑 Step 3: Providing password...")
        
        payload = {"message": "testpassword123"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Password provided successfully")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Failed to provide password - THIS IS THE ERROR")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw response: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_health_check():
    """Test the health check endpoint."""
    print("🏥 Testing health check...")
    response = requests.get("http://localhost:8000/health")
    
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
    
    data = response.json()
    print("✅ Health check passed")
    
    # Check intent service status
    intent_status = data.get("services", {}).get("intent_classification", {})
    if isinstance(intent_status, dict):
        print(f"Intent service ready: {intent_status.get('ready')}")
        print(f"Intent test result: {intent_status.get('test_result')}")
        if intent_status.get('error'):
            print(f"Intent error: {intent_status.get('error')}")
    
    # Add assertions for required fields
    assert "status" in data, "Missing 'status' in response"
    assert data["status"] == "healthy", f"Expected status 'healthy', got {data.get('status')}"
    assert "version" in data, "Missing 'version' in response"
    assert "services" in data, "Missing 'services' in response"

def main():
    """Run the login flow test."""
    print("🧪 Login Flow Debug Test")
    print("=" * 60)
    
    # Check if server is running
    if not test_health_check():
        print("\n❌ Server health check failed")
        print("Please ensure the server is running: python run.py")
        return False
    
    print("\n" + "="*60)
    
    # Test the login flow
    success = test_login_flow()
    
    print("\n" + "="*60)
    if success:
        print("🎉 Login flow test completed successfully!")
    else:
        print("❌ Login flow test failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
