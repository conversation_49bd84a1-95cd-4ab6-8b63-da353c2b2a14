#!/usr/bin/env python3
"""
Startup script that handles dependency installation and runs the application.

This script ensures all dependencies are installed before starting the API.
"""

import sys
import subprocess
import os
from pathlib import Path

def install_dependencies():
    """Install required dependencies."""
    print("🔧 Checking and installing dependencies...")
    
    try:
        # Install main requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        
        # Ensure pydantic-settings is installed (for Pydantic v2 compatibility)
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "pydantic-settings"
        ])
        
        print("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_environment():
    """Check if environment variables are set."""
    print("🔍 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found. Creating from template...")
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("📝 Created .env file from .env.example")
            print("🔑 Please edit .env file with your API keys before running the application")
            return False
        else:
            print("❌ No .env.example file found")
            return False
    
    # Check for required environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ["HF_TOKEN", "GEMINI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing required environment variables: {', '.join(missing_vars)}")
        print("🔑 Please set these in your .env file")
        return False
    
    print("✅ Environment configuration looks good!")
    return True

def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing application imports...")
    
    try:
        # Test basic imports
        from app.core.config import Settings
        from app.models.requests import MessageRequest
        print("✅ Core imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def start_application():
    """Start the FastAPI application."""
    print("🚀 Starting the application...")
    
    try:
        # Import and run the application
        from run import main
        main()
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        return False
    
    return True

def main():
    """Main startup function."""
    print("🎯 Onboarding API Startup Script")
    print("=" * 40)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed. Exiting.")
        sys.exit(1)
    
    # Step 2: Test imports
    if not test_imports():
        print("❌ Import tests failed. Please check your installation.")
        sys.exit(1)
    
    # Step 3: Check environment
    if not check_environment():
        print("❌ Environment check failed. Please configure your .env file.")
        sys.exit(1)
    
    # Step 4: Start application
    print("\n🎉 All checks passed! Starting the application...")
    start_application()

if __name__ == "__main__":
    main()
