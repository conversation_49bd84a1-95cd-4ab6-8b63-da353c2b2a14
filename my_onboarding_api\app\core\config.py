"""
Configuration management for the application.

This module handles environment variables, validation, and application settings.
"""

import os
from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings with validation."""
    
    # API Configuration
    app_name: str = Field(default="Onboarding Intent & Gemini Chat API", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")

    # MCP Server Configuration
    mcp_host: str = Field(default="0.0.0.0", description="MCP server host")
    mcp_port: int = Field(default=5000, description="MCP server port")
    api_base_url: str = Field(default="http://localhost:8000", description="Base URL for API server", alias="API_BASE_URL")
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    cors_allow_credentials: bool = Field(default=True, description="Allow CORS credentials")
    cors_allow_methods: List[str] = Field(default=["*"], description="Allowed CORS methods")
    cors_allow_headers: List[str] = Field(default=["*"], description="Allowed CORS headers")
    
    # External API Configuration
    hf_token: str = Field(..., description="Hugging Face API token", alias="HF_TOKEN")
    model_name: str = Field(..., description="Hugging Face model name", alias="MODEL_NAME")
    gemini_api_key: str = Field(..., description="Google Gemini API key", alias="GEMINI_API_KEY")
    gemini_model: str = Field(default="models/gemini-2.5-flash", description="Gemini model name", alias="GEMINI_MODEL")
    
    # Authentication API Configuration
    auth_api_url: str = Field(
        default="https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/setAuthToken",
        description="Authentication API URL"
    )
    signup_api_url: str = Field(
        default="https://6s0bso8sri.execute-api.us-east-1.amazonaws.com/amplifydev/userCreatev2",
        description="Signup API URL"
    )
    auth_api_timeout: int = Field(default=10, description="Authentication API timeout in seconds")

    # API Authorization Token
    api_bearer_token: Optional[str] = Field(
        default=None,
        description="Bearer token for API authorization",
        alias="API_BEARER_TOKEN"
    )
    
    # Intent Classification Configuration
    confidence_threshold: float = Field(default=0.85, description="Intent classification confidence threshold")
    
    # Security Configuration
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    
    @field_validator("hf_token")
    @classmethod
    def validate_hf_token(cls, v):
        """Validate Hugging Face token."""
        if not v or not v.startswith("hf_"):
            raise ValueError("Invalid Hugging Face token format")
        return v

    @field_validator("gemini_api_key")
    @classmethod
    def validate_gemini_api_key(cls, v):
        """Validate Gemini API key."""
        if not v or len(v) < 20:
            raise ValueError("Invalid Gemini API key")
        return v

    @field_validator("confidence_threshold")
    @classmethod
    def validate_confidence_threshold(cls, v):
        """Validate confidence threshold."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


# Global settings instance
settings = Settings()
