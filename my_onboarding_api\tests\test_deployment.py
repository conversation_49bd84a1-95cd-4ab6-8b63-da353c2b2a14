#!/usr/bin/env python3
"""
Test script to verify deployment is working correctly.

This script tests both the API server and MCP server endpoints.
"""

import requests
import sys
import os
from typing import Dict, Any

def test_api_health(base_url: str) -> bool:
    """Test API server health endpoint."""
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ API Health Check: {data.get('status', 'unknown')}")
        
        # Check services status
        services = data.get('services', {})
        for service, status in services.items():
            if isinstance(status, dict):
                ready = status.get('ready', False)
                print(f"   - {service}: {'✅' if ready else '❌'} {status}")
            else:
                print(f"   - {service}: {'✅' if status else '❌'}")
        
        return data.get('status') == 'healthy'
        
    except Exception as e:
        print(f"❌ API Health Check Failed: {e}")
        return False

def test_api_intent_classification(base_url: str) -> bool:
    """Test intent classification endpoint."""
    try:
        test_message = "I want to sign up for an account"
        response = requests.post(
            f"{base_url}/onboarding",
            json={"message": test_message},
            timeout=30
        )
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Intent Classification: {len(data)} results")
        
        # Show top result
        if data and len(data) > 0:
            top_result = data[0]
            print(f"   - Top intent: {top_result.get('label', 'unknown')} "
                  f"(confidence: {top_result.get('score', 0):.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Intent Classification Failed: {e}")
        return False

def test_mcp_server(base_url: str, mcp_port: int = 5000) -> bool:
    """Test MCP server accessibility."""
    try:
        # Try to connect to MCP server port
        mcp_url = f"{base_url.replace('https://', 'https://').replace('http://', 'http://')}:{mcp_port}"
        
        # Note: This is a basic connectivity test
        # Full MCP testing would require the FastMCP client
        response = requests.get(mcp_url, timeout=10)
        
        print(f"✅ MCP Server Accessible at {mcp_url}")
        return True
        
    except Exception as e:
        print(f"❌ MCP Server Test: {e}")
        print(f"   Note: MCP server might be running but not accessible via HTTP GET")
        print(f"   Use FastMCP client for proper testing")
        return False

def main():
    """Main test function."""
    # Get base URL from environment or command line
    base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🧪 Testing deployment at: {base_url}")
    print("=" * 50)
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    # Test 1: API Health
    if test_api_health(base_url):
        tests_passed += 1
    
    # Test 2: Intent Classification
    if test_api_intent_classification(base_url):
        tests_passed += 1
    
    # Test 3: MCP Server
    mcp_port = int(os.getenv("MCP_PORT", "5000"))
    if test_mcp_server(base_url, mcp_port):
        tests_passed += 1
    
    print("=" * 50)
    print(f"Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Deployment is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
