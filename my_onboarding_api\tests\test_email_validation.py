#!/usr/bin/env python3
"""
Test script to verify email validation handling in the login flow.
"""

import requests
import json
import sys
import uuid

def test_invalid_email_login():
    """Test login flow with invalid email format."""
    base_url = "http://localhost:8000"
    session_id = f"test-session-{uuid.uuid4()}"
    
    print("📧 Testing Invalid Email Validation in Login Flow")
    print("=" * 60)
    print(f"Session ID: {session_id}")
    
    try:
        # Step 1: Initiate login flow
        print("\n📝 Step 1: Initiating login flow...")
        
        payload = {"message": "I want to log in"}
        headers = {
            "Content-Type": "application/json",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Failed to initiate login flow")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        print(f"✅ Login flow initiated")
        print(f"Response: {json.dumps(data, indent=2)}")
        
        # Step 2: Provide INVALID email (this is the test case)
        print("\n📧 Step 2: Providing INVALID email (just 'vimal')...")
        
        payload = {"message": "vimal"}  # Invalid email format
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Failed to provide email")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        print(f"✅ Email step completed")
        print(f"Response: {json.dumps(data, indent=2)}")
        
        # Step 3: Provide password (this should trigger the validation error)
        print("\n🔑 Step 3: Providing password (this should trigger validation error)...")
        
        payload = {"message": "testpassword123"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            # Check if we got a proper validation error response
            if data.get("success") is False and "email" in data.get("message", "").lower():
                print("✅ Got expected validation error for invalid email!")
                return True
            elif data.get("status") == "success":
                print("❌ Unexpected success - validation should have failed")
                return False
            else:
                print("✅ Got error response (checking if it's the right type)")
                return True
        else:
            print(f"❌ Got HTTP error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error response: {json.dumps(error_data, indent=2)}")
                
                # Check if it's a validation error (422) instead of 500
                if response.status_code == 422:
                    print("✅ Got 422 validation error - this is better than 500!")
                    return True
                elif response.status_code == 500:
                    print("❌ Still getting 500 error - fix didn't work")
                    return False
            except:
                print(f"Raw response: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_valid_email_login():
    """Test login flow with valid email format."""
    base_url = "http://localhost:8000"
    session_id = f"test-session-{uuid.uuid4()}"
    
    print("\n✅ Testing Valid Email in Login Flow")
    print("=" * 50)
    print(f"Session ID: {session_id}")
    
    try:
        # Step 1: Initiate login flow
        payload = {"message": "I want to log in"}
        headers = {
            "Content-Type": "application/json",
            "session_id": session_id
        }
        
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to initiate login flow")
            return False
        
        # Step 2: Provide VALID email
        print("📧 Providing VALID email...")
        
        payload = {"message": "<EMAIL>"}  # Valid email format
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to provide email")
            return False
        
        # Step 3: Provide password
        print("🔑 Providing password...")
        
        payload = {"message": "testpassword123"}
        response = requests.post(
            f"{base_url}/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Valid email flow completed")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Got error with valid email: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Valid email test failed: {e}")
        return False

def main():
    """Run the email validation tests."""
    print("🧪 Email Validation Test Suite")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server health check failed")
            return False
    except:
        print("❌ Server not accessible")
        return False
    
    print("✅ Server is running")
    
    # Test invalid email
    invalid_email_result = test_invalid_email_login()
    
    # Test valid email
    valid_email_result = test_valid_email_login()
    
    print("\n" + "="*70)
    print("📊 Test Results:")
    print(f"   Invalid Email Handling: {'✅ PASS' if invalid_email_result else '❌ FAIL'}")
    print(f"   Valid Email Handling: {'✅ PASS' if valid_email_result else '❌ FAIL'}")
    
    if invalid_email_result and valid_email_result:
        print("\n🎉 All email validation tests passed!")
        return True
    else:
        print("\n❌ Some email validation tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
