"""
Backward compatibility module for the original main.py.

This module imports the new modular application while maintaining
backward compatibility with existing deployment scripts.
"""

# Import the new modular application
from app.main import app

# For backward compatibility, expose the original classes and functions
from app.models.requests import MessageRequest as Message
from app.services.intent_service import intent_service
from app.services.gemini_service import gemini_service
from app.services.auth_service import auth_service
from app.services.session_service import session_service

# Expose the pipeline and model for backward compatibility
pipe = intent_service._pipeline
gemini_model = gemini_service._model

# Expose sessions for backward compatibility
sessions = session_service._sessions

# Expose the original functions for backward compatibility
def call_signup_api(data: dict):
    """Backward compatibility wrapper for signup API."""
    from app.models.requests import SignupRequest
    signup_request = SignupRequest(**data)
    return auth_service.signup(signup_request)

def call_login_api(data: dict):
    """Backward compatibility wrapper for login API."""
    from app.models.requests import LoginRequest
    login_request = LoginRequest(
        email=data.get("email"),
        password=data.get("password")
    )
    try:
        return auth_service.login(login_request)
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "data": None
        }

# The app variable is already imported from app.main
# This ensures existing deployment scripts continue to work
