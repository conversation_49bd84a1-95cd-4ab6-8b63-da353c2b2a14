#!/usr/bin/env python3
"""
Application runner script.

This script provides a convenient way to run the application with proper configuration.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Main entry point for running the application."""
    try:
        # Import after path setup
        from app.core.config import settings
        from app.core.logging import setup_logging
        
        # Set up logging
        setup_logging()
        
        # Import uvicorn
        import uvicorn
        
        print(f"Starting {settings.app_name} v{settings.app_version}")
        print(f"Server will run on {settings.host}:{settings.port}")
        print(f"Debug mode: {settings.debug}")
        print(f"Log level: {settings.log_level}")
        
        # Run the application
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True,
            server_header=False,  # Security: don't expose server info
            date_header=False     # Security: don't expose date info
        )
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
