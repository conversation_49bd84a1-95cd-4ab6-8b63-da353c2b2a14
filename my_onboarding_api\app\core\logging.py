"""
Logging configuration for the application.

This module sets up structured logging with proper formatting and levels.
"""

import logging
import sys
from typing import Optional
from datetime import datetime

from .config import settings


class CustomFormatter(logging.Formatter):
    """Custom formatter with colors and structured output."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        """Format log record with colors and structure."""
        # Add timestamp
        record.timestamp = datetime.utcnow().isoformat()
        
        # Add color for console output
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.colored_levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        # Format the message
        formatted = super().format(record)
        return formatted


def setup_logging(
    level: Optional[str] = None,
    format_string: Optional[str] = None
) -> None:
    """
    Set up application logging.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Custom format string for log messages
    """
    # Use settings defaults if not provided
    log_level = level or settings.log_level
    log_format = format_string or settings.log_format
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set up custom formatter for console output
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(CustomFormatter(log_format))
    
    # Configure application logger
    app_logger = logging.getLogger("onboarding_api")
    app_logger.setLevel(getattr(logging, log_level))
    
    # Remove default handlers and add our custom one
    app_logger.handlers.clear()
    app_logger.addHandler(console_handler)
    app_logger.propagate = False
    
    # Configure third-party loggers
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    app_logger.info(f"Logging configured with level: {log_level}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(f"onboarding_api.{name}")


# Request logging middleware helper
def log_request(method: str, url: str, status_code: int, duration: float) -> None:
    """
    Log HTTP request information.
    
    Args:
        method: HTTP method
        url: Request URL
        status_code: Response status code
        duration: Request duration in seconds
    """
    logger = get_logger("requests")
    
    level = logging.INFO
    if status_code >= 400:
        level = logging.WARNING
    if status_code >= 500:
        level = logging.ERROR
    
    logger.log(
        level,
        f"{method} {url} - {status_code} - {duration:.3f}s"
    )
