"""
Test API routes.

This module tests the API endpoints to ensure they work correctly.
"""

import pytest
from fastapi import status


class TestHealthEndpoint:
    """Test the health check endpoint."""
    
    def test_health_check(self, client):
        """Test health check returns correct status."""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "services" in data


class TestOnboardingEndpoint:
    """Test the onboarding intent classification endpoint."""
    
    def test_onboarding_intent_success(self, client, mock_intent_service, sample_message_request):
        """Test successful intent classification."""
        response = client.post("/onboarding", json=sample_message_request)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["input"] == sample_message_request["message"]
        assert "intent" in data
        assert "message" in data
    
    def test_onboarding_intent_empty_message(self, client, mock_intent_service):
        """Test intent classification with empty message."""
        response = client.post("/onboarding", json={"message": ""})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_onboarding_intent_invalid_request(self, client, mock_intent_service):
        """Test intent classification with invalid request."""
        response = client.post("/onboarding", json={})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestGeminiChatEndpoint:
    """Test the Gemini chat endpoint."""
    
    def test_gemini_chat_success(self, client, mock_gemini_service, sample_message_request):
        """Test successful Gemini chat."""
        response = client.post("/gemini-chat", json=sample_message_request)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert data["input"] == sample_message_request["message"]
        assert "response" in data
        assert "message" in data
    
    def test_gemini_chat_empty_message(self, client, mock_gemini_service):
        """Test Gemini chat with empty message."""
        response = client.post("/gemini-chat", json={"message": ""})
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestCombinedChatEndpoint:
    """Test the combined chat with intent detection endpoint."""
    
    def test_combined_chat_success(self, client, mock_intent_service, mock_gemini_service, 
                                 sample_message_request, test_headers):
        """Test successful combined chat."""
        response = client.post(
            "/gemini-chat-with-intent", 
            json=sample_message_request,
            headers=test_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
    
    def test_combined_chat_missing_session_id(self, client, mock_intent_service, 
                                            mock_gemini_service, sample_message_request):
        """Test combined chat without session ID."""
        response = client.post("/gemini-chat-with-intent", json=sample_message_request)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_combined_chat_signup_flow(self, client, mock_intent_service, mock_gemini_service, 
                                     test_headers):
        """Test signup flow initiation."""
        # Mock intent service to return signup intent
        mock_intent_service.get_top_intent.return_value = "signup"
        
        response = client.post(
            "/gemini-chat-with-intent",
            json={"message": "I want to sign up"},
            headers=test_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "flow_step" in data
        assert "flow_type" in data
        assert data["flow_type"] == "signup"
    
    def test_combined_chat_login_flow(self, client, mock_intent_service, mock_gemini_service, 
                                    test_headers):
        """Test login flow initiation."""
        # Mock intent service to return login intent
        mock_intent_service.get_top_intent.return_value = "login"
        
        response = client.post(
            "/gemini-chat-with-intent",
            json={"message": "I want to log in"},
            headers=test_headers
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "flow_step" in data
        assert "flow_type" in data
        assert data["flow_type"] == "login"


class TestGeminiModelsEndpoint:
    """Test the Gemini models listing endpoint."""
    
    def test_list_gemini_models_success(self, client, mock_gemini_service):
        """Test successful model listing."""
        response = client.get("/gemini-models")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["success"] is True
        assert "available_models" in data
        assert len(data["available_models"]) > 0
