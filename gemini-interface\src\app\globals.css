@import "tailwindcss";

/* ==========================================
   ⚡ Modern Design System (2025 Edition)
   Primary: Tech Blue | Secondary: Electric Violet | Accent: Emerald
   Neutral tones and soft gradients
   ========================================== */

:root {
  /* Light mode (default) */
  --background: 0 0% 100%; /* White */
  --foreground: 222 47% 11%; /* Deep slate text */

  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;

  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11%;

  --primary: 210 90% 56%; /* Modern blue (#1e90ff) */
  --primary-foreground: 0 0% 100%;

  --secondary: 268 71% 60%; /* Electric violet (#9b51e0) */
  --secondary-foreground: 0 0% 100%;

  --muted: 220 14% 96%; /* Soft gray background */
  --muted-foreground: 220 9% 46%;

  --accent: 158 64% 52%; /* Emerald (#10b981) */
  --accent-foreground: 0 0% 100%;

  --destructive: 0 84% 60%; /* Red */
  --destructive-foreground: 0 0% 100%;

  --border: 220 13% 91%;
  --input: 0 0% 100%;
  --ring: 210 90% 56%; /* Matches primary blue */
  --radius: 0.75rem;
}

/* ==========================================
   🌙 Dark Mode — Futuristic & Soft Contrast
   ========================================== */
.dark {
  --background: 222 47% 8%; /* Deep slate */
  --foreground: 210 40% 96%; /* Soft light gray */

  --card: 222 47% 10%;
  --card-foreground: 210 40% 96%;

  --popover: 222 47% 10%;
  --popover-foreground: 210 40% 96%;

  --primary: 210 90% 66%; /* Bright blue pop */
  --primary-foreground: 222 47% 8%;

  --secondary: 268 71% 70%; /* Vibrant violet */
  --secondary-foreground: 222 47% 8%;

  --muted: 223 15% 20%; /* Muted gray surface */
  --muted-foreground: 220 10% 65%;

  --accent: 158 64% 60%; /* Neon emerald */
  --accent-foreground: 222 47% 8%;

  --destructive: 0 80% 65%;
  --destructive-foreground: 0 0% 100%;

  --border: 223 15% 20%;
  --input: 223 15% 18%;
  --ring: 210 90% 66%;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* ==========================================
   🧩 Base Styles
   ========================================== */

* {
  border-color: hsl(var(--border));
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-geist-sans), Inter, system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================
   🖱️ Custom Scrollbar (Modern)
   ========================================== */

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* ==========================================
   ✨ Focus & Animation Utilities
   ========================================== */

.focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.animate-message-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-typing {
  animation: typing 1.5s ease-in-out infinite;
}

/* ==========================================
   🌅 Gradients
   ========================================== */

.bg-modern-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--secondary)) 100%
  );
}

.bg-modern-gradient-soft {
  background: linear-gradient(
    135deg,
    hsl(var(--muted)) 0%,
    hsl(var(--background)) 100%
  );
}

/* ==========================================
   🎬 Keyframes
   ========================================== */

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}
