from fastmcp import Client
from pathlib import Path # Import Path

# Construct the absolute path to mcp_server.py
# Assuming tests/test_mcp.py is in 'tests' and mcp_server.py is one level up:
server_path = Path(__file__).parent.parent / "mcp_server.py"

# Create client pointing to the absolute path of your MCP server script
# Use str() to ensure it's passed as a simple string path
client = Client(str(server_path)) 

# Call classify_intent tool
intent_result = client.classify_intent(message="I want to sign up")
print("Intent result:", intent_result)

# ... (rest of the client code)