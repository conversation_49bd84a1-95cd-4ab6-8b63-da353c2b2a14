#!/usr/bin/env python3
"""
Test script to verify the complete signup flow.

This script simulates the conversation flow to ensure all parameters are collected.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.session_service import SessionService
from app.models.session import FlowType, FlowStep
from app.core.logging import get_logger

logger = get_logger(__name__)


def test_signup_flow():
    """Test the complete signup flow."""
    print("Testing Signup Flow")
    print("=" * 50)
    
    session_service = SessionService()
    session_id = "test_session_123"
    
    # Start signup flow
    print("\n1. Starting signup flow...")
    session_service.start_flow(session_id, FlowType.SIGNUP)
    
    # Get initial message
    message = session_service.get_flow_message(session_id)
    print(f"Initial message: {message}")
    
    # Simulate user responses
    test_responses = [
        ("name", "<PERSON>"),
        ("first<PERSON><PERSON>", "<PERSON>"),
        ("lastName", "Doe"),
        ("email", "<EMAIL>"),
        ("password", "securepassword123"),
        ("phoneNumber", "******-123-4567"),
        ("cityId", "city_123"),
        ("city", "New York"),
        ("state", "NY"),
        ("zipCode", "10001"),
        ("streetAddressOne", "123 Main Street"),
        ("streetAddressTwo", "Apt 4B"),
        ("role", "citizen"),
        ("assignedRole", "resident"),
        ("userType", "individual"),
        ("type", "standard"),
        ("registeredFrom", "web_app"),
        ("userAddedFrom", "self_registration"),
        ("createdBy", "skip"),
        ("isStakeholder", "yes"),
        ("isAssociated", "no"),
        ("gender", "prefer_not_to_say"),
        ("birthday", "1990-01-15"),
        ("citiesArray", "city_123, city_456")
    ]
    
    print("\n2. Simulating user responses...")
    step_count = 0
    
    for expected_field, response in test_responses:
        step_count += 1
        session = session_service.get_session(session_id)
        
        if not session_service.is_flow_active(session_id):
            print(f"Flow completed after {step_count-1} steps")
            break
            
        current_step = session.flow_step.value if session.flow_step else "unknown"
        print(f"\nStep {step_count}: {current_step}")
        print(f"Expected field: {expected_field}")
        print(f"User response: {response}")
        
        # Verify we're on the expected step
        if current_step != expected_field:
            print(f"WARNING: Expected step '{expected_field}' but got '{current_step}'")
        
        try:
            # Advance the flow
            next_step = session_service.advance_flow(session_id, current_step, response)
            
            if next_step:
                next_message = session_service.get_flow_message(session_id)
                print(f"Next step: {next_step.value}")
                print(f"Next message: {next_message}")
            else:
                print("Flow will complete after this step")
                
        except Exception as e:
            print(f"ERROR during step {step_count}: {str(e)}")
            break
    
    # Complete the flow
    print("\n3. Completing flow...")
    try:
        if session_service.is_flow_active(session_id):
            user_data = session_service.complete_flow(session_id)
            print("Flow completed successfully!")
            
            print("\n4. Collected data:")
            data_dict = user_data.to_dict()
            for key, value in data_dict.items():
                print(f"  {key}: {value}")
                
            print(f"\nTotal fields collected: {len(data_dict)}")
            
            # Check for required fields
            required_fields = ['name', 'firstName', 'lastName', 'email', 'password']
            missing_required = [field for field in required_fields if field not in data_dict]
            
            if missing_required:
                print(f"WARNING: Missing required fields: {missing_required}")
            else:
                print("✓ All required fields collected")
                
            # Check for optional fields
            optional_fields = ['phoneNumber', 'cityId', 'city', 'state', 'zipCode', 
                             'streetAddressOne', 'streetAddressTwo', 'role', 'assignedRole',
                             'userType', 'type', 'registeredFrom', 'userAddedFrom',
                             'isStakeholder', 'isAssociated', 'gender', 'birthday', 'citiesArray']
            
            collected_optional = [field for field in optional_fields if field in data_dict]
            print(f"✓ Optional fields collected: {len(collected_optional)}/{len(optional_fields)}")
            print(f"  Collected: {collected_optional}")
            
        else:
            print("Flow was already completed")
            
    except Exception as e:
        print(f"ERROR completing flow: {str(e)}")
        
    print("\n" + "=" * 50)
    print("Test completed")


def test_skip_functionality():
    """Test the skip functionality for optional fields."""
    print("\n\nTesting Skip Functionality")
    print("=" * 50)
    
    session_service = SessionService()
    session_id = "test_skip_session"
    
    # Start signup flow
    session_service.start_flow(session_id, FlowType.SIGNUP)
    
    # Test required fields first
    required_responses = [
        ("name", "Jane Smith"),
        ("firstName", "Jane"),
        ("lastName", "Smith"),
        ("email", "<EMAIL>"),
        ("password", "password123")
    ]
    
    print("Providing required fields...")
    for field, response in required_responses:
        session = session_service.get_session(session_id)
        current_step = session.flow_step.value
        session_service.advance_flow(session_id, current_step, response)
    
    # Now skip several optional fields
    skip_count = 0
    while session_service.is_flow_active(session_id):
        session = session_service.get_session(session_id)
        current_step = session.flow_step.value
        
        print(f"Skipping field: {current_step}")
        session_service.advance_flow(session_id, current_step, "skip")
        skip_count += 1
        
        # Safety check to avoid infinite loop
        if skip_count > 20:
            print("Safety break - too many steps")
            break
    
    # Complete flow
    user_data = session_service.complete_flow(session_id)
    data_dict = user_data.to_dict()
    
    print(f"\nSkipped {skip_count} optional fields")
    print(f"Final collected data: {list(data_dict.keys())}")
    print("✓ Skip functionality working correctly")


if __name__ == "__main__":
    test_signup_flow()
    test_skip_functionality()
