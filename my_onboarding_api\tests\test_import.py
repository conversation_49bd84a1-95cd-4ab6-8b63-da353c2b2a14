#!/usr/bin/env python3
"""
Simple test script to verify imports work correctly.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly."""
    try:
        print("Testing pydantic-settings import...")
        from pydantic_settings import BaseSettings
        print("✓ pydantic-settings imported successfully")

        print("Testing core config import...")
        from app.core.config import Settings
        print("✓ Settings class imported successfully")

        # Test creating settings without requiring environment variables
        print("Testing settings creation...")
        try:
            from app.core.config import settings
            print(f"✓ Config loaded successfully. App name: {settings.app_name}")
        except Exception as config_error:
            print(f"⚠️ Config loading failed (expected if .env missing): {config_error}")
            print("This is normal if HF_TOKEN and GEMINI_API_KEY are not set")

        print("Testing model imports...")
        from app.models.requests import MessageRequest, LoginRequest, SignupRequest
        print("✓ Request models imported successfully")

        print("Testing basic model creation...")
        msg = MessageRequest(message="test message")
        print(f"✓ MessageRequest created: {msg.message}")

        print("\n🎉 Core imports successful! The Pydantic v2 compatibility fixes work correctly.")
        return True

    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
