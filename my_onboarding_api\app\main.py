"""
Main FastAPI application.

This module creates and configures the FastAPI application with all middleware,
routes, and error handlers.
"""

from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidation<PERSON>rror
from starlette.exceptions import HTTPException as StarletteHTTPException

from .core.config import settings
from .core.logging import setup_logging, get_logger
from .core.exceptions import OnboardingAPIException
from .api.routes import router
from .middleware.logging_middleware import LoggingMiddleware
from .middleware.security_middleware import RateLimitMiddleware, SecurityHeadersMiddleware
from .services.intent_service import intent_service
from .services.gemini_service import gemini_service

# Set up logging
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("Starting Onboarding API application")
    logger.info(f"Configuration: Debug={settings.debug}, Log Level={settings.log_level}")
    
    # Verify services are ready
    from .services.intent_service import intent_service
    from .services.gemini_service import gemini_service
    
    if not intent_service.is_pipeline_ready():
        logger.error("Intent classification service not ready")
        raise RuntimeError("Intent classification service initialization failed")
    
    if not gemini_service.is_service_ready():
        logger.error("Gemini service not ready")
        raise RuntimeError("Gemini service initialization failed")
    
    logger.info("All services initialized successfully")
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Onboarding API application")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    # Create FastAPI app
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="API for onboarding intent classification and Gemini chat functionality",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=settings.cors_allow_methods,
        allow_headers=settings.cors_allow_headers,
    )
    
    # Add custom middleware (order matters!)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Include API routes
    app.include_router(router, prefix="", tags=["onboarding"])
    
    # Add exception handlers
    add_exception_handlers(app)
    
    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        # Test intent service with a simple message
        intent_status = {
            "ready": intent_service.is_pipeline_ready(),
            "test_result": "not_tested",
            "error": None
        }

        if intent_status["ready"]:
            try:
                test_results = intent_service.classify_intent("hello")
                intent_status["test_result"] = "working" if test_results else "failed"
            except Exception as e:
                intent_status["error"] = str(e)
                intent_status["test_result"] = "error"

        return {
            "status": "healthy",
            "version": settings.app_version,
            "services": {
                "intent_classification": intent_status,
                "gemini": gemini_service.is_service_ready()
            }
        }
    
    return app


def add_exception_handlers(app: FastAPI) -> None:
    """
    Add custom exception handlers to the application.
    
    Args:
        app: FastAPI application
    """
    
    @app.exception_handler(OnboardingAPIException)
    async def onboarding_exception_handler(request: Request, exc: OnboardingAPIException):
        """Handle custom application exceptions."""
        logger.error(f"Application exception: {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Application Error",
                "message": str(exc),
                "details": exc.details
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle request validation errors."""
        logger.warning(f"Validation error: {str(exc)}")
        return JSONResponse(
            status_code=422,
            content={
                "error": "Validation Error",
                "message": "Invalid request data",
                "details": exc.errors()
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """Handle HTTP exceptions."""
        logger.warning(f"HTTP exception: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "HTTP Error",
                "message": str(exc.detail),
                "status_code": exc.status_code
            }
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """Handle ValueError exceptions."""
        logger.warning(f"Value error: {str(exc)}")
        return JSONResponse(
            status_code=400,
            content={
                "error": "Bad Request",
                "message": str(exc),
                "type": "ValueError"
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        logger.error(f"Unexpected exception: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": str(exc) if settings.debug else "An unexpected error occurred",
                "type": exc.__class__.__name__
            }
        )


# Create the application instance
app = create_app()

# For backward compatibility, keep the original variable name
# This ensures existing deployment scripts continue to work
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
