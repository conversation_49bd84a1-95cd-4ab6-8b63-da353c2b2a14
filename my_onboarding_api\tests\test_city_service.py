"""
Test script for CityService to find exact city matches in DynamoDB.
"""
import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file first thing
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import city_service after environment variables are loaded
from app.services.city_service import get_city_service, city_service

def display_city(city):
    """Display city details in a formatted way."""
    if not city:
        return
        
    print("\nFound city:")
    print("-" * 40)
    print(f"Name: {city.get('name', 'N/A')}")
    print(f"ID: {city.get('id', 'N/A')}")
    print(f"Code: {city.get('code', 'N/A')}")
    print(f"Created At: {city.get('createdAt', 'N/A')}")
    print(f"Updated At: {city.get('updatedAt', 'N/A')}")
    print("-" * 40)
    print("All available fields:")
    for key, value in city.items():
        print(f"  {key}: {value}")
    print("-" * 40)

def check_aws_credentials():
    """Check if AWS credentials are properly configured."""
    required_vars = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY',
        'AWS_REGION'
    ]
    
    missing = [var for var in required_vars if not os.getenv(var)]
    if missing:
        logger.warning(f"Missing required AWS environment variables: {', '.join(missing)}")
        return False
    return True

if __name__ == "__main__":
    # Verify AWS credentials
    if not check_aws_credentials():
        logger.error("AWS credentials are not properly configured. Please set the following environment variables:")
        logger.error("  - AWS_ACCESS_KEY_ID")
        logger.error("  - AWS_SECRET_ACCESS_KEY")
        logger.error("  - AWS_REGION (optional, defaults to 'us-east-1')")
        exit(1)
    
    # Get the service instance after environment is fully set up
    service = get_city_service()
    
    logger.info("Starting City Lookup Utility")
    logger.info(f"Using DynamoDB table: {os.getenv('DYNAMODB_CITIES_TABLE', 'cities')}")
    
    while True:
        try:
            city_name = input("\nEnter city name (or 'q' to quit): ").strip()
            
            if city_name.lower() == 'q':
                print("Goodbye!")
                break
                
            if not city_name:
                print("Please enter a city name")
                continue
                
            logger.info(f"Searching for city: {city_name}")
            city = service.find_city_by_name(city_name)
            
            if city:
                display_city(city)
            else:
                print(f"\nNo city found with name: {city_name}")
                
        except Exception as e:
            logger.error(f"An error occurred: {str(e)}", exc_info=True)
            print(f"\nAn error occurred while searching for the city. Please check the logs for more details.")
