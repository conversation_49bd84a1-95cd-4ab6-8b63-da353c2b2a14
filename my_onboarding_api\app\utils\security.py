"""
Security utilities for input validation and sanitization.

This module provides security-related utility functions.
"""

import re
import html
from typing import Optional, List
from urllib.parse import urlparse

from ..core.logging import get_logger

logger = get_logger(__name__)


def sanitize_input(text: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize user input to prevent XSS and other attacks.
    
    Args:
        text: Input text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
    """
    if not text:
        return ""
    
    # HTML escape
    sanitized = html.escape(text.strip())
    
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', sanitized)
    
    # Limit length if specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
        logger.warning(f"Input truncated to {max_length} characters")
    
    return sanitized


def validate_email(email: str) -> bool:
    """
    Validate email format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if email format is valid
    """
    if not email:
        return False
    
    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email.lower()) is not None


def validate_url(url: str, allowed_schemes: Optional[List[str]] = None) -> bool:
    """
    Validate URL format and scheme.
    
    Args:
        url: URL to validate
        allowed_schemes: List of allowed schemes (default: ['http', 'https'])
        
    Returns:
        True if URL is valid
    """
    if not url:
        return False
    
    if allowed_schemes is None:
        allowed_schemes = ['http', 'https']
    
    try:
        parsed = urlparse(url)
        return (
            parsed.scheme in allowed_schemes and
            parsed.netloc and
            len(parsed.netloc) > 0
        )
    except Exception:
        return False


def is_safe_string(text: str, allow_special_chars: bool = False) -> bool:
    """
    Check if string contains only safe characters.
    
    Args:
        text: Text to check
        allow_special_chars: Whether to allow special characters
        
    Returns:
        True if string is safe
    """
    if not text:
        return True
    
    if allow_special_chars:
        # Allow alphanumeric, spaces, and common punctuation
        pattern = r'^[a-zA-Z0-9\s\.,!?\-_@#$%&*()+=\[\]{}|;:\'\"]+$'
    else:
        # Only alphanumeric and spaces
        pattern = r'^[a-zA-Z0-9\s]+$'
    
    return re.match(pattern, text) is not None


def detect_potential_injection(text: str) -> bool:
    """
    Detect potential SQL injection or script injection attempts.
    
    Args:
        text: Text to analyze
        
    Returns:
        True if potential injection detected
    """
    if not text:
        return False
    
    # Common injection patterns
    injection_patterns = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(<script[^>]*>.*?</script>)',
        r'(javascript:)',
        r'(on\w+\s*=)',
        r'(\b(eval|setTimeout|setInterval)\s*\()',
        r'(data:text/html)',
    ]
    
    text_lower = text.lower()
    
    for pattern in injection_patterns:
        if re.search(pattern, text_lower, re.IGNORECASE | re.DOTALL):
            logger.warning(f"Potential injection detected: {pattern}")
            return True
    
    return False


def generate_session_id() -> str:
    """
    Generate a secure session ID.
    
    Returns:
        Random session ID
    """
    import secrets
    return secrets.token_urlsafe(32)


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """
    Mask sensitive data for logging.
    
    Args:
        data: Sensitive data to mask
        mask_char: Character to use for masking
        visible_chars: Number of characters to keep visible
        
    Returns:
        Masked data
    """
    if not data or len(data) <= visible_chars:
        return mask_char * len(data) if data else ""
    
    return data[:visible_chars] + mask_char * (len(data) - visible_chars)
