#!/usr/bin/env python3
"""
Test the API endpoints to debug the runtime error.
"""

import requests
import json
import sys

def test_health():
    """Test the health endpoint."""
    try:
        print("🏥 Testing health endpoint...")
        response = requests.get("http://localhost:8000/health")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"📊 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_debug_intent():
    """Test the debug intent endpoint."""
    try:
        print("\n🔍 Testing debug intent endpoint...")
        
        payload = {"message": "I want to sign up"}
        response = requests.post(
            "http://localhost:8000/debug-intent",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Debug intent test passed")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Debug intent test failed")
            print(f"📊 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Debug intent test error: {e}")
        return False

def test_onboarding():
    """Test the onboarding endpoint."""
    try:
        print("\n🎯 Testing onboarding endpoint...")
        
        payload = {"message": "I want to sign up"}
        response = requests.post(
            "http://localhost:8000/onboarding",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Onboarding test passed")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Onboarding test failed")
            try:
                error_data = response.json()
                print(f"📊 Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📊 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Onboarding test error: {e}")
        return False

def test_gemini_chat_with_intent():
    """Test the gemini chat with intent endpoint."""
    try:
        print("\n💬 Testing gemini chat with intent endpoint...")
        
        payload = {"message": "I want to sign up"}
        headers = {
            "Content-Type": "application/json",
            "session_id": "test-session-123"
        }
        
        response = requests.post(
            "http://localhost:8000/gemini-chat-with-intent",
            json=payload,
            headers=headers
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Gemini chat with intent test passed")
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Gemini chat with intent test failed")
            try:
                error_data = response.json()
                print(f"📊 Error response: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📊 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Gemini chat with intent test error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 API Endpoint Testing")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
    except requests.exceptions.RequestException:
        print("❌ Server not running or not accessible at http://localhost:8000")
        print("   Please start the server with: python run.py")
        return False
    
    print("✅ Server is running")
    
    # Run tests
    tests = [
        ("Health Check", test_health),
        ("Debug Intent", test_debug_intent),
        ("Onboarding", test_onboarding),
        ("Gemini Chat with Intent", test_gemini_chat_with_intent)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
