"""
Test the updated signup API implementation.

This module tests the signup API with all the new parameters.
"""

import pytest
from unittest.mock import Mock, patch
from app.models.requests import SignupRequest
from app.services.auth_service import AuthenticationService
from app.core.exceptions import AuthenticationError, ExternalServiceError


class TestSignupRequest:
    """Test SignupRequest model with all parameters."""
    
    def test_minimal_signup_request(self):
        """Test signup request with minimal required fields."""
        request = SignupRequest(
            name="<PERSON>",
            firstName="<PERSON>",
            lastName="Doe",
            email="<EMAIL>",
            password="password123"
        )
        
        assert request.name == "<PERSON>"
        assert request.firstName == "John"
        assert request.lastName == "Doe"
        assert request.email == "<EMAIL>"
        assert request.password == "password123"
        
    def test_full_signup_request(self):
        """Test signup request with all parameters."""
        request = SignupRequest(
            name="<PERSON>",
            firstName="<PERSON>",
            lastName="Doe",
            email="<EMAIL>",
            password="password123",
            phoneNumber="+1234567890",
            cityId="city_123",
            city="New York",
            state="NY",
            zipCode="10001",
            streetAddressOne="123 Main St",
            streetAddressTwo="Apt 4B",
            role="citizen",
            assignedRole="resident",
            userType="individual",
            type="standard",
            registeredFrom="web_app",
            userAddedFrom="self_registration",
            createdBy="admin_123",
            isStakeholder=True,
            isAssociated=False,
            gender="prefer_not_to_say",
            birthday="1990-01-01",
            citiesArray=["city_123", "city_456"]
        )
        
        assert request.cityId == "city_123"
        assert request.phoneNumber == "+1234567890"
        assert request.isStakeholder is True
        assert request.citiesArray == ["city_123", "city_456"]
        
    def test_invalid_email_validation(self):
        """Test email validation."""
        with pytest.raises(ValueError, match="Invalid email format"):
            SignupRequest(
                name="John Doe",
                firstName="John",
                lastName="Doe",
                email="invalid-email",
                password="password123"
            )
            
    def test_invalid_phone_validation(self):
        """Test phone number validation."""
        with pytest.raises(ValueError, match="Invalid phone number format"):
            SignupRequest(
                name="John Doe",
                firstName="John",
                lastName="Doe",
                email="<EMAIL>",
                password="password123",
                phoneNumber="invalid-phone"
            )
            
    def test_invalid_birthday_validation(self):
        """Test birthday validation."""
        with pytest.raises(ValueError, match="Birthday must be in YYYY-MM-DD format"):
            SignupRequest(
                name="John Doe",
                firstName="John",
                lastName="Doe",
                email="<EMAIL>",
                password="password123",
                birthday="invalid-date"
            )


class TestAuthenticationServiceSignup:
    """Test AuthenticationService signup method."""
    
    @patch('app.services.auth_service.requests.post')
    def test_successful_signup(self, mock_post):
        """Test successful signup API call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "User created successfully",
            "user": {"id": "user_123", "email": "<EMAIL>"}
        }
        mock_post.return_value = mock_response
        
        # Create signup request
        signup_data = SignupRequest(
            name="John Doe",
            firstName="John",
            lastName="Doe",
            email="<EMAIL>",
            password="password123",
            cityId="city_123",
            phoneNumber="+1234567890"
        )
        
        # Test signup
        auth_service = AuthenticationService()
        result = auth_service.signup(signup_data)
        
        # Verify result
        assert result["status"] == "success"
        assert result["message"] == "Signup completed successfully"
        assert "user" in result
        assert result["user"]["email"] == "<EMAIL>"
        
        # Verify API call was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        # Check URL
        assert "userCreatev2" in call_args[0][0]
        
        # Check headers
        headers = call_args[1]["headers"]
        assert headers["Content-Type"] == "application/json"
        assert "Authorization" in headers or "User-Agent" in headers
        
        # Check request data
        request_data = call_args[1]["json"]
        assert request_data["email"] == "<EMAIL>"
        assert request_data["firstName"] == "John"
        assert request_data["lastName"] == "Doe"
        assert request_data["cityId"] == "city_123"
        assert request_data["phoneNumber"] == "+1234567890"
        assert "password" in request_data
        
    @patch('app.services.auth_service.requests.post')
    def test_signup_api_failure(self, mock_post):
        """Test signup API failure."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "success": False,
            "message": "Email already exists"
        }
        mock_post.return_value = mock_response
        
        # Create signup request
        signup_data = SignupRequest(
            name="John Doe",
            firstName="John",
            lastName="Doe",
            email="<EMAIL>",
            password="password123"
        )
        
        # Test signup failure
        auth_service = AuthenticationService()
        
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.signup(signup_data)
            
        assert "Email already exists" in str(exc_info.value)
        
    @patch('app.services.auth_service.requests.post')
    def test_signup_timeout(self, mock_post):
        """Test signup API timeout."""
        from requests.exceptions import Timeout
        
        # Mock timeout
        mock_post.side_effect = Timeout("Request timeout")
        
        # Create signup request
        signup_data = SignupRequest(
            name="John Doe",
            firstName="John",
            lastName="Doe",
            email="<EMAIL>",
            password="password123"
        )
        
        # Test timeout
        auth_service = AuthenticationService()
        
        with pytest.raises(ExternalServiceError) as exc_info:
            auth_service.signup(signup_data)
            
        assert "timeout" in str(exc_info.value).lower()


if __name__ == "__main__":
    pytest.main([__file__])
