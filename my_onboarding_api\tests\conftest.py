"""
Test configuration and fixtures.

This module provides common test fixtures and configuration.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from app.main import create_app
from app.core.config import settings


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def mock_intent_service():
    """Mock intent classification service."""
    with patch('app.services.intent_service.intent_service') as mock:
        mock.classify_intent.return_value = [
            {"label": "signup", "score": 0.95},
            {"label": "login", "score": 0.05}
        ]
        mock.get_top_intent.return_value = "signup"
        mock.is_pipeline_ready.return_value = True
        yield mock


@pytest.fixture
def mock_gemini_service():
    """Mock Gemini AI service."""
    with patch('app.services.gemini_service.gemini_service') as mock:
        mock.generate_response.return_value = "This is a test response from <PERSON>."
        mock.generate_chat_response.return_value = "This is a test chat response."
        mock.list_available_models.return_value = [
            {"name": "models/gemini-2.5-flash", "supported_generation_methods": ["generateContent"]}
        ]
        mock.is_service_ready.return_value = True
        yield mock


@pytest.fixture
def mock_auth_service():
    """Mock authentication service."""
    with patch('app.services.auth_service.auth_service') as mock:
        mock.login.return_value = {
            "status": "success",
            "message": "Login successful",
            "data": {"user_id": "123", "token": "test_token"}
        }
        mock.signup.return_value = {
            "status": "success",
            "message": "Signup completed successfully",
            "user": {"name": "Test User", "email": "<EMAIL>"}
        }
        yield mock


@pytest.fixture
def sample_message_request():
    """Sample message request data."""
    return {"message": "I want to create an account"}


@pytest.fixture
def sample_session_id():
    """Sample session ID for testing."""
    return "test-session-123"


@pytest.fixture
def test_headers(sample_session_id):
    """Test headers with session ID."""
    return {"session_id": sample_session_id}
