"""
Response models for API endpoints.

This module defines Pydantic models for API responses.
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model with common fields."""
    
    success: bool = Field(default=True, description="Whether the request was successful")
    message: Optional[str] = Field(default=None, description="Response message")


class ErrorResponse(BaseResponse):
    """Error response model."""
    
    success: bool = Field(default=False, description="Always false for errors")
    error: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")


class IntentClassificationResult(BaseModel):
    """Intent classification result."""
    
    label: str = Field(..., description="Predicted intent label")
    score: float = Field(..., description="Confidence score")


class OnboardingResponse(BaseResponse):
    """Response model for onboarding intent classification."""
    
    input: str = Field(..., description="Original user input")
    intent: List[IntentClassificationResult] = Field(..., description="Intent classification results")


class GeminiChatResponse(BaseResponse):
    """Response model for Gemini chat."""
    
    input: str = Field(..., description="Original user input")
    response: str = Field(..., description="Gemini AI response")


class CombinedChatResponse(BaseResponse):
    """Response model for combined chat with intent detection."""
    
    input: str = Field(..., description="Original user input")
    detected_intent: str = Field(..., description="Detected intent")
    gemini_response: Optional[str] = Field(default=None, description="Gemini AI response")
    flow_message: Optional[str] = Field(default=None, description="Flow-specific message")


class AuthenticationResponse(BaseResponse):
    """Response model for authentication."""
    
    status: str = Field(..., description="Authentication status")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Authentication data")


class SignupResponse(BaseResponse):
    """Response model for signup."""
    
    status: str = Field(..., description="Signup status")
    user: Optional[Dict[str, Any]] = Field(default=None, description="User data")


class GeminiModel(BaseModel):
    """Gemini model information."""
    
    name: str = Field(..., description="Model name")
    supported_generation_methods: List[str] = Field(..., description="Supported generation methods")


class GeminiModelsResponse(BaseResponse):
    """Response model for Gemini models listing."""
    
    available_models: List[GeminiModel] = Field(..., description="Available Gemini models")


class SessionFlowResponse(BaseResponse):
    """Response model for session flow interactions."""
    
    message: str = Field(..., description="Flow message to user")
    flow_step: Optional[str] = Field(default=None, description="Current flow step")
    flow_type: Optional[str] = Field(default=None, description="Type of flow (signup/login)")
    result: Optional[Union[AuthenticationResponse, SignupResponse]] = Field(
        default=None, 
        description="Final result when flow is complete"
    )
