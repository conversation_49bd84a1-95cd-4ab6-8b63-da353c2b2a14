#!/usr/bin/env python3
"""
Debug script to test the Hugging Face pipeline output format.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_pipeline():
    """Debug the Hugging Face pipeline output."""
    try:
        print("🔍 Debugging Hugging Face pipeline output...")
        
        # Import required modules
        from transformers import pipeline
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Get configuration
        hf_token = os.getenv("HF_TOKEN")
        model_name = os.getenv("MODEL_NAME", "jalpesh088/myvillage-onboarding-intent")
        
        if not hf_token:
            print("❌ HF_TOKEN not found in environment variables")
            return False
        
        print(f"📦 Model: {model_name}")
        print(f"🔑 Token: {hf_token[:10]}...")
        
        # Set the token
        os.environ["HUGGINGFACEHUB_API_TOKEN"] = hf_token
        
        # Create pipeline
        print("🚀 Creating pipeline...")
        pipe = pipeline(
            "text-classification",
            model=model_name,
            return_all_scores=True
        )
        
        # Test with sample text
        test_text = "I want to sign up for an account"
        print(f"🧪 Testing with: '{test_text}'")
        
        # Get results
        results = pipe(test_text)
        
        print("📊 Raw pipeline output:")
        print(f"Type: {type(results)}")
        print(f"Content: {results}")
        
        # Analyze the structure
        if isinstance(results, list):
            print(f"📋 Results is a list with {len(results)} items")
            if len(results) > 0:
                print(f"📋 First item type: {type(results[0])}")
                print(f"📋 First item content: {results[0]}")
                
                if isinstance(results[0], dict):
                    print(f"📋 First item keys: {list(results[0].keys())}")
                elif isinstance(results[0], list):
                    print(f"📋 First item is a list with {len(results[0])} items")
                    if len(results[0]) > 0:
                        print(f"📋 First sub-item: {results[0][0]}")
                        if isinstance(results[0][0], dict):
                            print(f"📋 First sub-item keys: {list(results[0][0].keys())}")
        
        # Try to process like the service does
        print("\n🔧 Testing service processing...")
        try:
            if isinstance(results, list) and len(results) > 0:
                # Check if results[0] is a list (batch processing)
                if isinstance(results[0], list):
                    # Take the first batch
                    actual_results = results[0]
                else:
                    # Results is already the list we want
                    actual_results = results
                
                print(f"📋 Processing {len(actual_results)} results")
                
                for i, result in enumerate(actual_results):
                    print(f"📋 Result {i}: {result}")
                    if isinstance(result, dict):
                        label = result.get("label", "unknown")
                        score = result.get("score", 0.0)
                        print(f"   ✓ Label: {label}, Score: {score}")
                    else:
                        print(f"   ❌ Unexpected result type: {type(result)}")
        
        except Exception as e:
            print(f"❌ Error processing results: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_pipeline()
    sys.exit(0 if success else 1)
