services:
  - type: web
    name: myvillage-onboarding-api
    env: python
    region: oregon
    plan: starter
    buildCommand: |
      cd my_onboarding_api && 
      pip install --upgrade pip && 
      pip install -r requirements.txt
    startCommand: cd my_onboarding_api && python deploy.py
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: API_BASE_URL
        value: https://myvillage-onboarding-api.onrender.com
      - key: MCP_HOST
        value: 0.0.0.0
      - key: MCP_PORT
        value: 5000
      - key: RUN_MODE
        value: both
      - key: HF_TOKEN
        sync: false
      - key: MODEL_NAME
        sync: false
      - key: GEMINI_API_KEY
        sync: false
      - key: GEMINI_MODEL
        value: models/gemini-2.5-flash
      - key: API_BEARER_TOKEN
        sync: false
    healthCheckPath: /health
    autoDeploy: true
    disk:
      name: myvillage-data
      mountPath: /opt/render/project/data
      sizeGB: 1
