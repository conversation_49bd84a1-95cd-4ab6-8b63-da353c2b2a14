"""
Test Pydantic models.

This module tests the request and response models for proper validation.
"""

import pytest
from pydantic import ValidationError

from app.models.requests import MessageRequest, LoginRequest, SignupRequest
from app.models.responses import (
    OnboardingResponse,
    GeminiChatResponse,
    CombinedChatResponse,
    IntentClassificationResult
)
from app.models.session import SessionData, FlowType, FlowStep, UserData


class TestMessageRequest:
    """Test MessageRequest model."""
    
    def test_valid_message_request(self):
        """Test valid message request creation."""
        request = MessageRequest(message="Hello, world!")
        assert request.message == "Hello, world!"
    
    def test_empty_message_validation(self):
        """Test validation of empty message."""
        with pytest.raises(ValidationError):
            MessageRequest(message="")
        
        with pytest.raises(ValidationError):
            MessageRequest(message="   ")
    
    def test_message_length_validation(self):
        """Test message length validation."""
        # Test maximum length
        long_message = "a" * 1001
        with pytest.raises(ValidationError):
            MessageRequest(message=long_message)
    
    def test_message_sanitization(self):
        """Test message sanitization."""
        request = MessageRequest(message="  Hello World  ")
        assert request.message.strip() == "Hello World"


class TestLoginRequest:
    """Test LoginRequest model."""
    
    def test_valid_login_request(self):
        """Test valid login request creation."""
        request = LoginRequest(
            email="<EMAIL>",
            password="password123"
        )
        assert request.email == "<EMAIL>"
        assert request.password == "password123"
    
    def test_email_validation(self):
        """Test email validation."""
        # Valid email
        request = LoginRequest(email="<EMAIL>", password="pass")
        assert request.email == "<EMAIL>"
        
        # Invalid emails
        with pytest.raises(ValidationError):
            LoginRequest(email="invalid-email", password="pass")
        
        with pytest.raises(ValidationError):
            LoginRequest(email="", password="pass")
    
    def test_password_validation(self):
        """Test password validation."""
        # Valid password
        request = LoginRequest(email="<EMAIL>", password="password123")
        assert request.password == "password123"
        
        # Empty password
        with pytest.raises(ValidationError):
            LoginRequest(email="<EMAIL>", password="")
        
        with pytest.raises(ValidationError):
            LoginRequest(email="<EMAIL>", password="   ")


class TestSignupRequest:
    """Test SignupRequest model."""
    
    def test_valid_signup_request(self):
        """Test valid signup request creation."""
        request = SignupRequest(
            name="John Doe",
            email="<EMAIL>",
            password="password123"
        )
        assert request.name == "John Doe"
        assert request.email == "<EMAIL>"
        assert request.password == "password123"
    
    def test_name_validation(self):
        """Test name validation."""
        # Valid name
        request = SignupRequest(
            name="John Doe",
            email="<EMAIL>",
            password="pass"
        )
        assert request.name == "John Doe"
        
        # Empty name
        with pytest.raises(ValidationError):
            SignupRequest(name="", email="<EMAIL>", password="pass")
        
        # Name too long
        long_name = "a" * 101
        with pytest.raises(ValidationError):
            SignupRequest(name=long_name, email="<EMAIL>", password="pass")
    
    def test_name_sanitization(self):
        """Test name sanitization."""
        request = SignupRequest(
            name="  John Doe  ",
            email="<EMAIL>",
            password="pass"
        )
        assert request.name == "John Doe"


class TestResponseModels:
    """Test response models."""
    
    def test_intent_classification_result(self):
        """Test IntentClassificationResult model."""
        result = IntentClassificationResult(label="signup", score=0.95)
        assert result.label == "signup"
        assert result.score == 0.95
    
    def test_onboarding_response(self):
        """Test OnboardingResponse model."""
        intent_results = [
            IntentClassificationResult(label="signup", score=0.95),
            IntentClassificationResult(label="login", score=0.05)
        ]
        
        response = OnboardingResponse(
            input="I want to sign up",
            intent=intent_results,
            message="Classification completed"
        )
        
        assert response.input == "I want to sign up"
        assert len(response.intent) == 2
        assert response.intent[0].label == "signup"
        assert response.success is True
    
    def test_gemini_chat_response(self):
        """Test GeminiChatResponse model."""
        response = GeminiChatResponse(
            input="Hello",
            response="Hi there!",
            message="Response generated"
        )
        
        assert response.input == "Hello"
        assert response.response == "Hi there!"
        assert response.success is True
    
    def test_combined_chat_response(self):
        """Test CombinedChatResponse model."""
        response = CombinedChatResponse(
            input="I need help",
            detected_intent="unknown",
            gemini_response="How can I help you?",
            message="Response generated"
        )
        
        assert response.input == "I need help"
        assert response.detected_intent == "unknown"
        assert response.gemini_response == "How can I help you?"
        assert response.success is True


class TestSessionModels:
    """Test session-related models."""
    
    def test_session_data(self):
        """Test SessionData model."""
        session = SessionData(
            flow_step=FlowStep.EMAIL,
            flow_type=FlowType.SIGNUP,
            collected_data={"name": "John"}
        )
        
        assert session.flow_step == FlowStep.EMAIL
        assert session.flow_type == FlowType.SIGNUP
        assert session.collected_data["name"] == "John"
    
    def test_session_data_defaults(self):
        """Test SessionData model with defaults."""
        session = SessionData()
        
        assert session.flow_step is None
        assert session.flow_type is None
        assert session.collected_data == {}
    
    def test_user_data(self):
        """Test UserData model."""
        user = UserData(
            name="John Doe",
            email="<EMAIL>",
            password="password123"
        )
        
        assert user.name == "John Doe"
        assert user.email == "<EMAIL>"
        assert user.password == "password123"
    
    def test_user_data_to_dict(self):
        """Test UserData model_dump method."""
        user = UserData(
            name="John Doe",
            email="<EMAIL>"
        )
        
        data_dict = user.model_dump(exclude_unset=True)
        assert "name" in data_dict
        assert "email" in data_dict
        assert "password" not in data_dict  # Should be excluded when not set
        assert data_dict["name"] == "John Doe"
        assert data_dict["email"] == "<EMAIL>"
    
    def test_flow_enums(self):
        """Test flow type and step enums."""
        # Test FlowType enum
        assert FlowType.SIGNUP == "signup"
        assert FlowType.LOGIN == "login"
        
        # Test FlowStep enum
        assert FlowStep.NAME == "name"
        assert FlowStep.EMAIL == "email"
        assert FlowStep.PASSWORD == "password"
