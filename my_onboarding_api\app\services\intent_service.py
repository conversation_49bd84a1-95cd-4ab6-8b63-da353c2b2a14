"""
Intent classification service using Hugging Face transformers.

This module provides intent classification functionality.
"""

import os
from typing import List, Optional
from transformers import pipeline, Pipeline

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import IntentClassificationError, ConfigurationError
from ..models.responses import IntentClassificationResult

logger = get_logger(__name__)


class IntentClassificationService:
    """Service for intent classification using Hugging Face models."""
    
    def __init__(self):
        """Initialize the intent classification service."""
        self._pipeline: Optional[Pipeline] = None
        self._initialize_pipeline()
    
    def _initialize_pipeline(self) -> None:
        """Initialize the Hugging Face pipeline."""
        try:
            # Set the Hugging Face token
            os.environ["HUGGINGFACEHUB_API_TOKEN"] = settings.hf_token
            
            logger.info(f"Initializing intent classification pipeline with model: {settings.model_name}")
            
            # Create the pipeline
            self._pipeline = pipeline(
                "text-classification",
                model=settings.model_name,
                top_k=None,  # Return all scores (replaces deprecated return_all_scores=True)
                device=-1  # Use CPU to avoid GPU issues
            )
            
            logger.info("Intent classification pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize intent classification pipeline: {str(e)}")
            raise ConfigurationError(
                f"Failed to initialize intent classification: {str(e)}",
                details={"model_name": settings.model_name}
            )
    
    def classify_intent(self, text: str) -> List[IntentClassificationResult]:
        """
        Classify the intent of the given text.
        
        Args:
            text: Input text to classify
            
        Returns:
            List of intent classification results
            
        Raises:
            IntentClassificationError: If classification fails
        """
        if not self._pipeline:
            raise IntentClassificationError("Intent classification pipeline not initialized")
        
        if not text or not text.strip():
            raise IntentClassificationError("Input text cannot be empty")
        
        try:
            logger.debug(f"Classifying intent for text: {text[:100]}...")

            # Get predictions
            raw_results = self._pipeline(text.strip())

            logger.debug(f"Raw pipeline output type: {type(raw_results)}")
            logger.debug(f"Raw pipeline output: {raw_results}")

            # Handle different output formats from Hugging Face pipeline
            if isinstance(raw_results, list):
                # Check if it's a batch result (list of lists) or direct result (list of dicts)
                if len(raw_results) > 0 and isinstance(raw_results[0], list):
                    # Batch processing - take the first batch
                    results = raw_results[0]
                    logger.debug("Using batch result format")
                elif len(raw_results) > 0 and isinstance(raw_results[0], dict):
                    # Direct result - list of dictionaries
                    results = raw_results
                    logger.debug("Using direct result format")
                else:
                    # Empty results or unexpected format
                    if len(raw_results) == 0:
                        logger.warning("Pipeline returned empty results")
                        # Return a default "unknown" result
                        return [IntentClassificationResult(label="unknown", score=0.0)]
                    else:
                        logger.error(f"Unexpected result item type: {type(raw_results[0])}")
                        raise IntentClassificationError(f"Unexpected result item type: {type(raw_results[0])}")
            else:
                # Unexpected format
                logger.error(f"Unexpected pipeline output format: {type(raw_results)}")
                raise IntentClassificationError(f"Unexpected pipeline output format: {type(raw_results)}")

            # Validate that results is a list of dictionaries
            if not isinstance(results, list):
                raise IntentClassificationError(f"Expected list of results, got {type(results)}")

            # Convert to our response model
            intent_results = []
            for i, result in enumerate(results):
                if not isinstance(result, dict):
                    logger.error(f"Result {i} is not a dictionary: {type(result)} - {result}")
                    raise IntentClassificationError(f"Expected dictionary result, got {type(result)}")

                if "label" not in result or "score" not in result:
                    logger.error(f"Result {i} missing required keys: {result}")
                    raise IntentClassificationError(f"Result missing required keys 'label' or 'score': {result}")

                intent_results.append(
                    IntentClassificationResult(
                        label=result["label"],
                        score=result["score"]
                    )
                )

            # Sort by confidence score (highest first)
            intent_results.sort(key=lambda x: x.score, reverse=True)

            logger.debug(f"Intent classification completed. Top intent: {intent_results[0].label} ({intent_results[0].score:.3f})")

            return intent_results
            
        except Exception as e:
            logger.error(f"Intent classification failed: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise IntentClassificationError(
                f"Failed to classify intent: {str(e)}",
                details={
                    "input_text": text[:100],
                    "exception_type": str(type(e)),
                    "traceback": traceback.format_exc()
                }
            )
    
    def get_top_intent(self, text: str, threshold: Optional[float] = None) -> str:
        """
        Get the top intent for the given text.
        
        Args:
            text: Input text to classify
            threshold: Confidence threshold (uses settings default if not provided)
            
        Returns:
            Top intent label or "unknown" if below threshold
        """
        confidence_threshold = threshold or settings.confidence_threshold
        
        results = self.classify_intent(text)
        
        if not results:
            return "unknown"
        
        top_result = results[0]
        
        if top_result.score >= confidence_threshold:
            return top_result.label
        else:
            logger.debug(f"Intent confidence {top_result.score:.3f} below threshold {confidence_threshold}")
            return "unknown"
    
    def is_pipeline_ready(self) -> bool:
        """Check if the pipeline is ready for use."""
        return self._pipeline is not None

    def test_pipeline_output(self, text: str) -> dict:
        """
        Test the raw pipeline output for debugging.

        Args:
            text: Input text to test

        Returns:
            Dictionary with debug information
        """
        if not self._pipeline:
            return {"error": "Pipeline not initialized"}

        try:
            raw_results = self._pipeline(text.strip())
            return {
                "input": text,
                "raw_results_type": str(type(raw_results)),
                "raw_results": raw_results,
                "success": True
            }
        except Exception as e:
            return {
                "input": text,
                "error": str(e),
                "success": False
            }


# Global service instance
intent_service = IntentClassificationService()
