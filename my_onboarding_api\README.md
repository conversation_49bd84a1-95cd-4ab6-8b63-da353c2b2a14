# Onboarding Intent & Gemini Chat API

A modern, production-ready FastAPI application that provides intelligent onboarding assistance through intent classification and conversational AI using Hugging Face transformers and Google's Gemini AI.

## 🚀 Features

- **Intent Classification**: Automatic detection of user intents (signup, login, etc.) using fine-tuned Hugging Face models
- **Conversational AI**: Natural language responses powered by Google's Gemini AI
- **MCP Server**: Model Context Protocol server for external tool integration
- **Session Management**: Stateful conversation flows for signup and login processes
- **Unified Deployment**: Single deployment script for both API and MCP servers
- **Security**: Rate limiting, input validation, security headers, and injection protection
- **Monitoring**: Comprehensive logging and request tracking
- **Production Ready**: Proper error handling, configuration management, and scalable architecture

## 📋 Table of Contents

- [Architecture](#architecture)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [MCP Server](#mcp-server)
- [Usage Examples](#usage-examples)
- [Development](#development)
- [Security](#security)
- [Deployment](#deployment)
- [Contributing](#contributing)

## 🏗️ Architecture

The application follows a modular, layered architecture with clear separation of concerns:

```
my_onboarding_api/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application factory
│   ├── core/                   # Core functionality
│   │   ├── config.py          # Configuration management
│   │   ├── logging.py         # Logging setup
│   │   └── exceptions.py      # Custom exceptions
│   ├── models/                 # Pydantic models
│   │   ├── requests.py        # Request models
│   │   ├── responses.py       # Response models
│   │   └── session.py         # Session models
│   ├── services/               # Business logic
│   │   ├── intent_service.py  # Intent classification
│   │   ├── gemini_service.py  # Gemini AI integration
│   │   ├── auth_service.py    # Authentication
│   │   └── session_service.py # Session management
│   ├── api/                    # API routes
│   │   └── routes.py          # Endpoint definitions
│   ├── middleware/             # Custom middleware
│   │   ├── logging_middleware.py
│   │   └── security_middleware.py
│   └── utils/                  # Utility functions
│       └── security.py        # Security utilities
├── main.py                     # Backward compatibility
├── mcp_server.py              # MCP (Model Context Protocol) server
├── deploy.py                  # Unified deployment script
├── test_deployment.py         # Deployment testing script
├── requirements.txt            # Production dependencies
├── requirements-dev.txt        # Development dependencies
├── pyproject.toml             # Project configuration
├── run.py                     # Application runner
├── Procfile                   # Process file for deployment
└── README.md                  # This file
```

### Key Architectural Principles

1. **Separation of Concerns**: Each module has a single responsibility
2. **Dependency Injection**: Services are loosely coupled and easily testable
3. **Configuration Management**: Environment-based configuration with validation
4. **Error Handling**: Comprehensive exception handling with proper logging
5. **Security First**: Input validation, rate limiting, and security headers
6. **Backward Compatibility**: Maintains compatibility with existing deployments

## 🛠️ Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Virtual environment (recommended)

### Quick Start

1. **Clone the repository** (if applicable):
   ```bash
   cd my_onboarding_api
   ```

2. **Create and activate a virtual environment**:
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt

   # If you get pydantic-settings import errors, install it separately:
   pip install pydantic-settings
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Run the application**:
   ```bash
   python run.py
   ```

The API will be available at `http://localhost:8000`

### Development Installation

For development with additional tools:

```bash
pip install -r requirements-dev.txt
```

## ⚙️ Configuration

The application uses environment variables for configuration. Create a `.env` file in the root directory:

```env
# Required API Keys
HF_TOKEN=your_huggingface_token_here
MODEL_NAME=jalpesh088/myvillage-onboarding-intent
GEMINI_API_KEY=your_gemini_api_key_here

# Optional Configuration
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Security Settings
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
CONFIDENCE_THRESHOLD=0.85

# External Services
AUTH_API_URL=https://your-auth-api.com/endpoint
AUTH_API_TIMEOUT=10
```

### Configuration Options

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `HF_TOKEN` | Hugging Face API token | - | Yes |
| `MODEL_NAME` | Hugging Face model name | - | Yes |
| `GEMINI_API_KEY` | Google Gemini API key | - | Yes |
| `DEBUG` | Enable debug mode | `false` | No |
| `LOG_LEVEL` | Logging level | `INFO` | No |
| `HOST` | Server host | `0.0.0.0` | No |
| `PORT` | Server port | `8000` | No |
| `RATE_LIMIT_REQUESTS` | Requests per minute | `100` | No |
| `CONFIDENCE_THRESHOLD` | Intent confidence threshold | `0.85` | No |

## 📚 API Documentation

### Base URL
```
http://localhost:8000
```

### Authentication
All endpoints require a `session_id` header for session management (except health check).

### Endpoints

#### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "services": {
    "intent_classification": true,
    "gemini": true
  }
}
```

#### Intent Classification
```http
POST /onboarding
Content-Type: application/json

{
  "message": "I want to create an account"
}
```

**Response:**
```json
{
  "success": true,
  "input": "I want to create an account",
  "intent": [
    {
      "label": "signup",
      "score": 0.95
    }
  ],
  "message": "Intent classification completed successfully"
}
```

#### Gemini Chat
```http
POST /gemini-chat
Content-Type: application/json

{
  "message": "Hello, how can I get started?"
}
```

**Response:**
```json
{
  "success": true,
  "input": "Hello, how can I get started?",
  "response": "Hello! I'd be happy to help you get started...",
  "message": "Chat response generated successfully"
}
```

#### Combined Chat with Intent Detection
```http
POST /gemini-chat-with-intent
Content-Type: application/json
session_id: your-session-id

{
  "message": "I want to sign up"
}
```

**Response (Flow Initiation):**
```json
{
  "success": true,
  "message": "Let's start signup! Please enter your name first.",
  "flow_step": "name",
  "flow_type": "signup"
}
```

**Response (Regular Chat):**
```json
{
  "success": true,
  "input": "How does this work?",
  "detected_intent": "unknown",
  "gemini_response": "This is an onboarding system that helps...",
  "message": "Response generated successfully"
}
```

#### List Gemini Models
```http
GET /gemini-models
```

**Response:**
```json
{
  "success": true,
  "available_models": [
    {
      "name": "models/gemini-2.5-flash",
      "supported_generation_methods": ["generateContent"]
    }
  ],
  "message": "Retrieved 1 available models"
}
```

### Error Responses

All endpoints return structured error responses:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "status_code": 400,
  "details": {
    "additional": "context"
  }
}
```

## 🔌 MCP Server

The application includes a Model Context Protocol (MCP) server that provides external tools for intent classification and chat functionality.

### MCP Tools

#### `classify_intent(message: str)`
Classifies the intent of a user message.

**Parameters:**
- `message`: The user message to classify

**Returns:**
- Dictionary containing intent classification results

#### `chat_with_intent(message: str, session_id: str)`
Processes chat messages with intent-aware Gemini AI.

**Parameters:**
- `message`: The user message
- `session_id`: Session identifier for conversation context

**Returns:**
- Dictionary containing chat response

### Using the MCP Server

```python
from fastmcp import Client

# Connect to MCP server (local)
client = Client("http://localhost:5000")

# Classify intent
result = client.classify_intent(message="I want to sign up")
print(result)

# Chat with intent
response = client.chat_with_intent(
    message="Hello, I need help with my account",
    session_id="user123"
)
print(response)
```

### Running MCP Server

```bash
# Run MCP server only
python mcp_server.py

# Run both API and MCP servers
python deploy.py
```

## 💡 Usage Examples

### Python Client Example

```python
import requests

# Set up session
session_id = "unique-session-id"
headers = {"session_id": session_id}
base_url = "http://localhost:8000"

# Start a conversation
response = requests.post(
    f"{base_url}/gemini-chat-with-intent",
    json={"message": "I want to create an account"},
    headers=headers
)

print(response.json())
# Output: {"message": "Let's start signup! Please enter your name first.", ...}

# Continue the flow
response = requests.post(
    f"{base_url}/gemini-chat-with-intent",
    json={"message": "John Doe"},
    headers=headers
)

print(response.json())
# Output: {"message": "Great! Now please enter your email.", ...}
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');

const client = axios.create({
  baseURL: 'http://localhost:8000',
  headers: {
    'session_id': 'unique-session-id',
    'Content-Type': 'application/json'
  }
});

async function startSignup() {
  try {
    const response = await client.post('/gemini-chat-with-intent', {
      message: 'I want to sign up'
    });

    console.log(response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
}

startSignup();
```

### cURL Example

```bash
# Health check
curl -X GET http://localhost:8000/health

# Intent classification
curl -X POST http://localhost:8000/onboarding \
  -H "Content-Type: application/json" \
  -d '{"message": "I want to log in"}'

# Combined chat with session
curl -X POST http://localhost:8000/gemini-chat-with-intent \
  -H "Content-Type: application/json" \
  -H "session_id: my-session-123" \
  -d '{"message": "Hello, I need help"}'
```

## 🔧 Development

### Setting Up Development Environment

1. **Install development dependencies**:
   ```bash
   pip install -r requirements-dev.txt
   ```

2. **Set up pre-commit hooks**:
   ```bash
   pre-commit install
   ```

3. **Run in development mode**:
   ```bash
   # With auto-reload
   python run.py

   # Or directly with uvicorn
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Code Quality Tools

- **Black**: Code formatting
  ```bash
  black app/
  ```

- **isort**: Import sorting
  ```bash
  isort app/
  ```

- **flake8**: Linting
  ```bash
  flake8 app/
  ```

- **mypy**: Type checking
  ```bash
  mypy app/
  ```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_intent_service.py
```

### Project Structure Guidelines

- **Models**: Define Pydantic models in `app/models/`
- **Services**: Business logic in `app/services/`
- **Routes**: API endpoints in `app/api/`
- **Middleware**: Custom middleware in `app/middleware/`
- **Utils**: Utility functions in `app/utils/`
- **Core**: Core functionality (config, logging, exceptions) in `app/core/`

## 🔒 Security

The application implements multiple security measures:

### Input Validation
- **Pydantic Models**: Automatic request validation
- **Sanitization**: HTML escaping and character filtering
- **Injection Protection**: Detection of SQL injection and XSS attempts
- **Length Limits**: Maximum input lengths enforced

### Rate Limiting
- **Per-IP Limits**: Configurable requests per minute
- **Sliding Window**: Efficient rate limiting algorithm
- **Headers**: Rate limit information in response headers

### Security Headers
- **X-Content-Type-Options**: Prevents MIME sniffing
- **X-Frame-Options**: Prevents clickjacking
- **X-XSS-Protection**: XSS protection
- **Content-Security-Policy**: CSP headers
- **Server Header Removal**: Hides server information

### Authentication & Sessions
- **Session Management**: Secure session handling
- **Token Validation**: API key validation
- **Timeout Handling**: Request timeouts for external APIs

### Best Practices
- **Environment Variables**: Sensitive data in environment variables
- **Logging**: Security events logged appropriately
- **Error Handling**: No sensitive information in error responses
- **HTTPS Ready**: Designed for HTTPS deployment

## 🚀 Deployment

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "run.py"]
```

Build and run:
```bash
docker build -t onboarding-api .
docker run -p 8000:8000 --env-file .env onboarding-api
```

### Production Considerations

1. **Environment Variables**: Use a secure method to manage environment variables
2. **Reverse Proxy**: Use nginx or similar for SSL termination and load balancing
3. **Database**: Replace in-memory session storage with Redis or database
4. **Monitoring**: Set up application monitoring and alerting
5. **Logging**: Configure centralized logging
6. **Scaling**: Consider horizontal scaling with load balancers

### Environment-Specific Configuration

- **Development**: `DEBUG=true`, detailed logging
- **Staging**: `DEBUG=false`, moderate logging
- **Production**: `DEBUG=false`, minimal logging, security headers

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**
4. **Run tests**: `pytest`
5. **Run code quality checks**: `black app/ && isort app/ && flake8 app/ && mypy app/`
6. **Commit your changes**: `git commit -m 'Add amazing feature'`
7. **Push to the branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

### Development Guidelines

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive docstrings
- Include tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. **Check the documentation** in this README
2. **Review the API documentation** at `/docs` (when running in debug mode)
3. **Check the logs** for error details
4. **Create an issue** for bugs or feature requests

### Common Issues

**Pydantic Import Errors:**
```bash
# If you see "BaseSettings has been moved to pydantic-settings"
pip install pydantic-settings

# If you see "cannot import name 'Field' from 'pydantic_settings'"
# This is already fixed in the current version
```

**Missing Environment Variables:**
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your actual API keys
# HF_TOKEN=your_huggingface_token
# GEMINI_API_KEY=your_gemini_api_key
```

**Module Import Errors:**
```bash
# Make sure you're in the correct directory
cd my_onboarding_api

# Run the test script to verify imports
python test_import.py
```

## 🔄 Changelog

### Version 1.0.0
- Initial release with modular architecture
- Intent classification using Hugging Face
- Gemini AI integration
- Session management for signup/login flows
- Comprehensive security measures
- Production-ready configuration
- Backward compatibility with original implementation

---

**Built with ❤️ using FastAPI, Hugging Face Transformers, and Google Gemini AI**
